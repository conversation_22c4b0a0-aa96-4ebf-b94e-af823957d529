import { http } from "@/utils/http";
import { baseUrlApi } from "./util";

export interface BankDto {
  bankSK: number;
  bankName: string;
}

export interface PaginationParams {
  page?: number;
  rows?: number;
  sidx?: string;
  sord?: "asc" | "desc";
  keyword?: string;
}

export interface PageResult<T> {
  items: T[];
  total: number;
}

export interface ApiResponse<T> {
  state: number;
  message: string;
  data: T;
  total?: number;
}

// 列表（分页 + 关键词）
export const listBanks = (params: PaginationParams = {}) => {
  return http.get<ApiResponse<PageResult<BankDto>>, any>(
    baseUrlApi("banks"),
    { params }
  );
};

// 详情
export const getBankById = (id: number) => {
  return http.get<ApiResponse<BankDto>, any>(`${baseUrlApi("banks")}/${id}`);
};

// 新增
export const createBank = (data: { bankName: string }) => {
  return http.post<ApiResponse<BankDto>, typeof data>(baseUrlApi("banks"), {
    data
  });
};

// 更新
export const updateBank = (id: number, data: { bankName: string }) => {
  return http.request<ApiResponse<BankDto>>("put", `${baseUrlApi("banks")}/${id}`, {
    data
  });
};

// 删除
export const deleteBank = (id: number) => {
  return http.request<ApiResponse<boolean>>("delete", `${baseUrlApi("banks")}/${id}`);
};

// 拉取所有银行（用于下拉）
export const fetchAllBanks = async () => {
  const res = await listBanks({ page: 1, rows: 9999, sidx: "bankSK", sord: "asc" });
  // 兼容后端分页结构 data.items 或 data 为数组
  const items = Array.isArray((res as any).data)
    ? (res as any).data
    : (res as any).data?.items ?? [];
  return items as BankDto[];
};
