<template>
  <div>
    <el-select
      v-model="select.store"
      placeholder="请选择门店"
      class="search-input"
      clearable
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-date-picker
      v-model="select.time"
      type="datetimerange"
      start-placeholder="开始时间日期"
      end-placeholder="结束时间日期"
      format="YYYY-MM-DD HH:mm:ss"
      date-format="YYYY/MM/DD ddd"
      time-format="A hh:mm:ss"
      style="margin-right: 10px"
    />
    <el-button type="primary">
      <el-icon>
        <Search />
      </el-icon>
      查询
    </el-button>
    <el-button type="success">
      <el-icon><Upload /></el-icon>
      导出
    </el-button>
    <el-table
      :data="tableData"
      style="width: 98%; margin: 0px auto"
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column
        prop="date"
        label="名堂旗舰店"
        width="150"
        align="center"
      />
      <el-table-column label="看房服务" align="center">
        <el-table-column
          prop="name"
          label="自订自看"
          width="120"
          align="center"
        >
          <el-table-column
            prop="name"
            label="VIP房及以上"
            width="120"
            align="center"
          >
            <el-table-column
              prop="name"
              label="间数"
              width="120"
              align="center"
            />
            <el-table-column
              prop="name"
              label="金额"
              width="120"
              align="center"
            />
          </el-table-column>
          <el-table-column
            prop="name"
            label="大房及以下"
            width="120"
            align="center"
          >
            <el-table-column
              prop="name"
              label="间数"
              width="120"
              align="center"
            />
            <el-table-column
              prop="name"
              label="金额"
              width="120"
              align="center"
            />
          </el-table-column>
        </el-table-column>
        <el-table-column
          prop="name"
          label="公司派房(200元一间)"
          width="120"
          align="center"
        >
          <el-table-column
            prop="name"
            label="间数"
            width="120"
            align="center"
          />
          <el-table-column
            prop="name"
            label="金额"
            width="120"
            align="center"
          />
        </el-table-column>
        <el-table-column
          prop="name"
          label="同事推荐(200元一间)"
          width="120"
          align="center"
        >
          <el-table-column
            prop="name"
            label="间数"
            width="120"
            align="center"
          />
          <el-table-column
            prop="name"
            label="金额"
            width="120"
            align="center"
          />
        </el-table-column>
        <el-table-column
          prop="name"
          label="看房小计"
          width="120"
          align="center"
        />
      </el-table-column>
      <el-table-column prop="name" label="推荐服务" width="120" align="center">
        <el-table-column
          prop="name"
          label="推荐同事进房(100元一间)"
          width="120"
          align="center"
        >
          <el-table-column
            prop="name"
            label="间数"
            width="120"
            align="center"
          />
          <el-table-column
            prop="name"
            label="金额"
            width="120"
            align="center"
          />
        </el-table-column>
      </el-table-column>
      <el-table-column prop="name" label="业绩提成" width="120" align="center">
        <el-table-column
          prop="name"
          label="自订自看(6%)"
          width="120"
          align="center"
        >
          <el-table-column
            prop="name"
            label="营业额"
            width="120"
            align="center"
          />
          <el-table-column
            prop="name"
            label="提成"
            width="120"
            align="center"
          />
        </el-table-column>
        <el-table-column
          prop="name"
          label="自订非自看(6%)"
          width="120"
          align="center"
        >
          <el-table-column
            prop="name"
            label="营业额"
            width="120"
            align="center"
          />
          <el-table-column
            prop="name"
            label="提成"
            width="120"
            align="center"
          />
        </el-table-column>
        <el-table-column
          prop="name"
          label="公司派房(6%)"
          width="120"
          align="center"
        >
          <el-table-column
            prop="name"
            label="营业额"
            width="120"
            align="center"
          />
          <el-table-column
            prop="name"
            label="提成"
            width="120"
            align="center"
          />
        </el-table-column>
        <el-table-column
          prop="name"
          label="同事推荐(6%)"
          width="120"
          align="center"
        >
          <el-table-column
            prop="name"
            label="营业额"
            width="120"
            align="center"
          />
          <el-table-column
            prop="name"
            label="提成"
            width="120"
            align="center"
          />
        </el-table-column>
        <el-table-column
          prop="name"
          label="业绩提成小计"
          width="120"
          align="center"
        />
        <el-table-column
          prop="name"
          label="门店经费提取(10%)"
          width="120"
          align="center"
        />
      </el-table-column>
      <el-table-column
        prop="name"
        label="个人业绩小计"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column prop="name" label="其他提成" width="120" align="center">
        <el-table-column
          prop="name"
          label="堂会代订房"
          width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="会员充值"
          width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="主题布置"
          width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="打碟提成"
          width="120"
          align="center"
        ></el-table-column>
      </el-table-column>
      <el-table-column
        prop="name"
        label="其他提成小计"
        width="120"
        align="center"
      />
      <el-table-column
        prop="name"
        label="个人提成合计"
        width="120"
        align="center"
        fixed="right"
      />
    </el-table>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { Search, Upload } from "@element-plus/icons-vue";
// 顶部选择器与时间栏绑定数据
const select = ref({
  store: "",
  time: []
});
//  选择器数据
const options = [
  {
    value: "名堂旗舰店",
    label: "名堂旗舰店"
  },
  {
    value: "天河店",
    label: "天河店"
  },
  {
    value: "缤缤店",
    label: "缤缤店"
  },
  {
    value: "海印店",
    label: "海印店"
  },
  {
    value: "白云店",
    label: "白云店"
  },
  {
    value: "英德店",
    label: "英德店"
  },
  {
    value: "区庄店",
    label: "区庄店"
  }
];
// 表格数据
const tableData = ref([
  {
    date: "2016-05-03",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036"
  },
  {
    date: "2016-05-02",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036"
  },
  {
    date: "2016-05-04",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036"
  },
  {
    date: "2016-05-01",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036"
  },
  {
    date: "2016-05-08",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036"
  },
  {
    date: "2016-05-06",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036"
  },
  {
    date: "2016-05-07",
    name: "Tom",
    state: "California",
    city: "Los Angeles",
    address: "No. 189, Grove St, Los Angeles",
    zip: "CA 90036"
  }
]);
</script>

<style scoped>
.search-input {
  width: 300px;
  margin: 10px 10px;
}
</style>
