<template>
  <div class="page-wrapper">
    <el-card class="mb-4" shadow="never">
      <div class="filter-form">
        <el-form :inline="true" :model="query" label-width="90px">
          <el-form-item label="银行">
            <el-select
              v-model="query.bankSk"
              placeholder="选择银行"
              clearable
              filterable
              class="w-60"
            >
              <el-option
                v-for="b in banks"
                :key="b.bankSK"
                :label="b.bankName"
                :value="b.bankSK"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="券编号">
            <el-input
              v-model.trim="query.fdNo"
              placeholder="请输入券编号"
              clearable
              class="w-60"
            />
          </el-form-item>
          <el-form-item label="券名称">
            <el-input
              v-model.trim="query.dealName"
              placeholder="请输入券名称"
              clearable
              class="w-60"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card shadow="never">
      <div class="toolbar mb-3">
        <el-button type="primary" @click="openBankDialog()">新增银行</el-button>
        <el-button type="success" @click="openDealDialog()"
          >新增银行券</el-button
        >
      </div>

      <el-table
        :data="tableData"
        border
        stripe
        @sort-change="onSortChange"
        :height="tableHeight"
      >
        <el-table-column
          prop="dealSK"
          label="ID"
          width="80"
          sortable="custom"
        />
        <el-table-column
          prop="bankSK"
          label="银行"
          width="140"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ bankNameMap.get(row.bankSK) ?? row.bankSK }}
          </template>
        </el-table-column>
        <el-table-column
          prop="fdNo"
          label="券编号"
          min-width="140"
          sortable="custom"
        />
        <el-table-column
          prop="dealName"
          label="券名称"
          min-width="160"
          sortable="custom"
        />
        <el-table-column
          prop="dealAmount"
          label="面额"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.dealAmount)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="subsidyAmount"
          label="补贴"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.subsidyAmount)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="serviceFee"
          label="服务费"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.serviceFee)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="netAmount"
          label="净额"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">{{
            formatCurrency(row.netAmount)
          }}</template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              size="small"
              @click="openDealDialog(row)"
              >编辑</el-button
            >
            <el-popconfirm
              title="确定删除该券吗？"
              @confirm="onDeleteDeal(row.dealSK)"
            >
              <template #reference>
                <el-button link type="danger" size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <div class="mt-3 flex justify-end">
        <el-pagination
          v-model:current-page="pager.page"
          v-model:page-size="pager.rows"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadData"
          @current-change="loadData"
        />
      </div>
    </el-card>

    <!-- 银行弹窗 -->
    <el-dialog
      v-model="bankDialog.visible"
      title="银行"
      width="420px"
      @closed="resetBankForm"
    >
      <el-form
        :model="bankDialog.form"
        :rules="bankRules"
        ref="bankFormRef"
        label-width="80px"
      >
        <el-form-item label="银行名称" prop="bankName">
          <el-input v-model.trim="bankDialog.form.bankName" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="bankDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="bankDialog.loading"
          @click="saveBank"
          >保存</el-button
        >
      </template>
    </el-dialog>

    <!-- 券弹窗 -->
    <el-dialog
      v-model="dealDialog.visible"
      :title="dealDialog.form.dealSK ? '编辑银行券' : '新增银行券'"
      width="680px"
      @closed="resetDealForm"
    >
      <el-form
        :model="dealDialog.form"
        :rules="dealRules"
        ref="dealFormRef"
        label-width="100px"
      >
        <el-form-item label="银行" prop="bankSK">
          <el-select
            v-model="dealDialog.form.bankSK"
            placeholder="选择银行"
            class="w-60"
          >
            <el-option
              v-for="b in banks"
              :key="b.bankSK"
              :label="b.bankName"
              :value="b.bankSK"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="券编号" prop="fdNo">
          <el-input v-model.trim="dealDialog.form.fdNo" />
        </el-form-item>
        <el-form-item label="券名称" prop="dealName">
          <el-input v-model.trim="dealDialog.form.dealName" />
        </el-form-item>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="面额" prop="dealAmount">
              <el-input-number
                v-model="dealDialog.form.dealAmount"
                :min="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补贴" prop="subsidyAmount">
              <el-input-number
                v-model="dealDialog.form.subsidyAmount"
                :min="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="服务费" prop="serviceFee">
              <el-input-number
                v-model="dealDialog.form.serviceFee"
                :min="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净额" prop="netAmount">
              <el-input-number
                v-model="dealDialog.form.netAmount"
                :min="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="dealDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="dealDialog.loading"
          @click="saveDeal"
          >保存</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { fetchAllBanks, createBank, BankDto } from "@/api/bank";
import {
  listBankDeals,
  createBankDeal,
  updateBankDeal,
  deleteBankDeal,
  BankDealDto
} from "@/api/bankDeal";

const tableHeight = computed(() => window.innerHeight - 360);

// 查询参数/分页
const query = reactive({
  bankSk: undefined as number | undefined,
  fdNo: "",
  dealName: "",
  sidx: "dealSK",
  sord: "desc" as "asc" | "desc"
});
const pager = reactive({ page: 1, rows: 20 });
const total = ref(0);

// 数据源
const banks = ref<BankDto[]>([]);
const bankNameMap = computed(
  () => new Map(banks.value.map(b => [b.bankSK, b.bankName]))
);
const tableData = ref<BankDealDto[]>([]);

// 加载银行与表格
const loadBanks = async () => {
  try {
    banks.value = await fetchAllBanks();
  } catch (e) {
    // 错误已在拦截器统一提示
  }
};

const loadData = async () => {
  try {
    const params = { ...query, page: pager.page, rows: pager.rows } as any;
    const res: any = await listBankDeals(params);
    const data = res?.data;
    // 兼容 data.items + total 或 data 为数组，total 在 res.total
    tableData.value = Array.isArray(data) ? data : (data?.items ?? []);
    total.value = (
      Array.isArray(data) ? data.length : (data?.total ?? res?.total ?? 0)
    ) as number;
  } catch (e) {
    tableData.value = [];
    total.value = 0;
  }
};

const onSearch = () => {
  pager.page = 1;
  loadData();
};
const onReset = () => {
  query.bankSk = undefined;
  query.fdNo = "";
  query.dealName = "";
  pager.page = 1;
  query.sidx = "dealSK";
  query.sord = "desc";
  loadData();
};

function onSortChange({ prop, order }: any) {
  if (!prop) return;
  query.sidx = prop;
  query.sord = order === "ascending" ? "asc" : "desc";
  loadData();
}

function formatCurrency(v?: number) {
  if (v == null) return "-";
  return Number(v).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

// 银行弹窗
const bankDialog = reactive({
  visible: false,
  loading: false,
  form: { bankName: "" }
});
const bankFormRef = ref<FormInstance>();
const bankRules: FormRules = {
  bankName: [{ required: true, message: "请输入银行名称", trigger: "blur" }]
};
function openBankDialog() {
  bankDialog.visible = true;
}
function resetBankForm() {
  bankDialog.loading = false;
  bankDialog.form.bankName = "";
}
async function saveBank() {
  await bankFormRef.value?.validate();
  bankDialog.loading = true;
  try {
    await createBank({ bankName: bankDialog.form.bankName });
    ElMessage.success("保存成功");
    bankDialog.visible = false;
    loadBanks();
  } finally {
    bankDialog.loading = false;
  }
}

// 券弹窗
const dealDialog = reactive<{
  visible: boolean;
  loading: boolean;
  form: Partial<BankDealDto>;
}>({ visible: false, loading: false, form: {} });
const dealFormRef = ref<FormInstance>();
const dealRules: FormRules = {
  bankSK: [{ required: true, message: "请选择银行", trigger: "change" }],
  fdNo: [{ required: true, message: "请输入券编号", trigger: "blur" }],
  dealName: [{ required: true, message: "请输入券名称", trigger: "blur" }]
};
function openDealDialog(row?: BankDealDto) {
  dealDialog.form = row ? { ...row } : { bankSK: query.bankSk };
  dealDialog.visible = true;
}
function resetDealForm() {
  dealDialog.loading = false;
  dealDialog.form = {};
}
async function saveDeal() {
  await dealFormRef.value?.validate();
  dealDialog.loading = true;
  try {
    const f = dealDialog.form;
    if (f.dealSK) {
      await updateBankDeal(f.dealSK, {
        bankSK: f.bankSK!,
        fdNo: f.fdNo,
        dealName: f.dealName,
        dealAmount: f.dealAmount,
        subsidyAmount: f.subsidyAmount,
        serviceFee: f.serviceFee,
        netAmount: f.netAmount,
        totalAmount: f.totalAmount
      });
    } else {
      await createBankDeal({
        bankSK: f.bankSK!,
        fdNo: f.fdNo,
        dealName: f.dealName,
        dealAmount: f.dealAmount,
        subsidyAmount: f.subsidyAmount,
        serviceFee: f.serviceFee,
        netAmount: f.netAmount,
        totalAmount: f.totalAmount
      });
    }
    ElMessage.success("保存成功");
    dealDialog.visible = false;
    loadData();
  } finally {
    dealDialog.loading = false;
  }
}

async function onDeleteDeal(id: number) {
  await deleteBankDeal(id);
  ElMessage.success("删除成功");
  loadData();
}

onMounted(async () => {
  await loadBanks();
  await loadData();
});
</script>

<style scoped>
.page-wrapper {
  padding: 12px;
}
.filter-form .w-60 {
  width: 240px;
}
.w-full {
  width: 100%;
}
.mb-3 {
  margin-bottom: 12px;
}
.mb-4 {
  margin-bottom: 16px;
}
</style>
