// import "@/utils/sso";
import Cookies from "js-cookie";
import { getConfig } from "@/config";
import NProgress from "@/utils/progress";
import { buildHierarchyTree } from "@/utils/tree";
import remainingRouter from "./modules/remaining";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { isUrl, openLink, storageLocal, isAllEmpty } from "@pureadmin/utils";
import {
  ascending,
  getTopMenu,
  initRouter,
  isOneOfArray,
  getHistoryMode,
  findRouteByPath,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes,
  addPathMatch
} from "./utils";
import {
  type Router,
  createRouter,
  type RouteRecordRaw,
  type RouteComponent
} from "vue-router";
import {
  type DataInfo,
  userKey,
  removeToken,
  multipleTabsKey,
  getToken
} from "@/utils/auth";
import { init } from "echarts/types/src/echarts.all.js";
import { get } from "sortablejs";

/** 自动导入全部静态路由，无需再手动引入！匹配 src/router/modules 目录（任何嵌套级别）中具有 .ts 扩展名的所有文件，除了 remaining.ts 文件
 * 如何匹配所有文件请看：https://github.com/mrmlnc/fast-glob#basic-syntax
 * 如何排除文件请看：https://cn.vitejs.dev/guide/features.html#negative-patterns
 */
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
    eager: true
  }
);

/** 原始静态路由（未做任何处理） */
const routes = [];

Object.keys(modules).forEach(key => {
  routes.push(modules[key].default);
});

/** 导出处理后的静态路由（三级及以上的路由全部拍成二级） */
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
);

/** 用于渲染菜单，保持原始层级 */
export const constantMenus: Array<RouteComponent> = ascending(
  routes.flat(Infinity)
).concat(...remainingRouter);

/** 不参与菜单的路由 */
export const remainingPaths = Object.keys(remainingRouter).map(v => {
  return remainingRouter[v].path;
});

/** 创建路由实例 */
export const router: Router = createRouter({
  history: getHistoryMode(import.meta.env.VITE_ROUTER_HISTORY),
  routes: constantRoutes.concat(...(remainingRouter as any)),
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    return new Promise(resolve => {
      if (savedPosition) {
        return savedPosition;
      } else {
        if (from.meta.saveSrollTop) {
          const top: number =
            document.documentElement.scrollTop || document.body.scrollTop;
          resolve({ left: 0, top });
        }
      }
    });
  }
});

/** 重置路由 */
export function resetRouter() {
  router.getRoutes().forEach(route => {
    const { name, meta } = route;
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name);
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(
          buildHierarchyTree(ascending(routes.flat(Infinity)))
        )
      );
    }
  });
  usePermissionStoreHook().clearAllCachePage();
}

/** 路由白名单 */
const whiteList = ["/login"];

const { VITE_HIDE_HOME } = import.meta.env;
// router.beforeEach((to: ToRouteType, _from, next) => {
//   // 新增逻辑：有 token 且不在首页时，先跳首页但不中断后续流程
//   const token = getToken()
//   const homePath = '/welcome'
//   // if (token && to.path !== homePath) {
//   //   next(homePath); // 先跳首页
//   //   // 不 return，继续执行后面的动态路由逻辑
//   // }

//   if (to.meta?.keepAlive) {
//     handleAliveRoute(to, "add");
//     // 页面整体刷新和点击标签页刷新
//     if (_from.name === undefined || _from.name === "Redirect") {
//       handleAliveRoute(to);
//     }
//   }

//   // 获取用户信息
//   const userInfo = storageLocal().getItem<DataInfo<number>>(userKey);
//   NProgress.start();

//   const externalLink = isUrl(to?.name as string);
//   if (!externalLink) {
//     to.matched.some(item => {
//       if (!item.meta.title) return "";
//       const Title = getConfig().Title;
//       if (Title) document.title = `${item.meta.title} | ${Title}`;
//       else document.title = item.meta.title as string;
//     });
//   }

//   /** 如果已经登录并存在登录信息后不能跳转到路由白名单，而是继续保持在当前页面 */
//   function toCorrectRoute() {
//     whiteList.includes(to.fullPath) ? next(_from.fullPath) : next();
//   }

//   // 避免重复加载动态路由的标志
//   const routeLoaded = window.sessionStorage.getItem('routeLoaded') === 'true';

//   if (Cookies.get(multipleTabsKey) && userInfo) {
//     // 无权限跳转403页面
//     if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles)) {
//       next({ path: "/error/403" });
//       return;
//     }

//     // 开启隐藏首页后在浏览器地址栏手动输入首页welcome路由则跳转到404页面
//     if (VITE_HIDE_HOME === "true" && to.fullPath === "/welcome") {
//       next({ path: "/error/404" });
//       return;
//     }

//     if (_from?.name) {
//       // name为超链接
//       if (externalLink) {
//         openLink(to?.name as string);
//         NProgress.done();
//         return;
//       } else {
//         toCorrectRoute();
//         return;
//       }
//     } else {
//       // 刷新逻辑
//       if (
//         to.path !== "/"
//       ) {
//         // 标记路由正在加载
//         window.sessionStorage.setItem('routeLoaded', 'true');
//         initRouter().then((router) => {
//           console.log("动态路由加载成功:", router);

//           if (!useMultiTagsStoreHook().getMultiTagsCache) {
//             const { path } = to;
//             const route = findRouteByPath(path, router.options.routes[0].children);
//             getTopMenu(true);

//             // query、params模式路由传参数的标签页不在此处处理
//             if (route && route.meta?.title) {
//               if (isAllEmpty(route.parentId) && route.meta?.backstage) {
//                 // 此处为动态顶级路由（目录）
//                 const { path, name, meta } = route.children[0];
//                 useMultiTagsStoreHook().handleTags("push", {
//                   path,
//                   name,
//                   meta,
//                 });
//               } else {
//                 const { path, name, meta } = route;
//                 useMultiTagsStoreHook().handleTags("push", {
//                   path,
//                   name,
//                   meta,
//                 });
//               }
//             }
//           }

//           // 如果是初始加载，重新导航到当前路径以确保路由完全匹配
//           if (isAllEmpty(to.name)) {
//             router.push(to.fullPath);
//             return;
//           }

//           toCorrectRoute();
//         }).catch((err) => {
//           console.error("路由初始化失败:", err);
//           window.sessionStorage.removeItem('routeLoaded');
//           next("/error/404");
//         });
//       } else {
//         // 已经加载过动态路由或不需要加载
//         console.log('使用已加载的路由');
//         toCorrectRoute();
//       }
//     }
//   } else {
//     if (whiteList.indexOf(to.path) !== -1) {
//       next();
//     } else {
//       removeToken();
//       next({ path: "/login" });
//     }
//   }
// });
// router.beforeEach((to, _from, next) => {
//   console.log('导航到:', to.path);
//   next();
// });
router.beforeEach((to: ToRouteType, from, next) => {
  const token = getToken();
  const systemSelectorPath = '/system-selector';

  // 登录后跳转到系统选择页面
  if (token && to.path !== systemSelectorPath && from.path === '/login') {
    return next(systemSelectorPath);
  }
  // 处理不需要登录的白名单路由
  if (!token && whiteList.includes(to.path)) {
    return next();
  }

  // 未登录且不在白名单
  if (!token) {
    removeToken();
    return next({ path: "/login" });
  }

  // 获取用户信息
  const userInfo = storageLocal().getItem<DataInfo<number>>(userKey);
  NProgress.start();

  // 设置页面标题
  const externalLink = isUrl(to?.name as string);
  if (!externalLink) {
    to.matched.some(item => {
      if (!item.meta.title) return "";
      const Title = getConfig().Title;
      document.title = Title
        ? `${item.meta.title} | ${Title}`
        : item.meta.title as string;
    });
  }

  // 权限检查
  function toCorrectRoute() {
    whiteList.includes(to.fullPath) ? next(from.fullPath) : next();
  }

  // 动态路由加载逻辑
  const routeLoaded = window.sessionStorage.getItem('routeLoaded') === 'true';
  const isRefreshing = !from.name; // 页面刷新的标志

  if (token) {
    // 权限不足跳转到403
    if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles)) {
      return next({ path: "/error/403" });
    }

    // 隐藏首页配置
    if (VITE_HIDE_HOME === "true" && to.fullPath === homePath) {
      return next({ path: "/error/404" });
    }

    // 外部链接处理
    if (externalLink) {
      openLink(to?.name as string);
      NProgress.done();
      return next(false);
    }

    // 页面刷新时重新加载动态路由
    if (isRefreshing && to.path !== "/") {
      // if (routeLoaded) {
      //   // 如果路由已加载，直接导航
      //   return toCorrectRoute();
      // } else {
      // 标记路由正在加载
      window.sessionStorage.setItem('routeLoaded', 'true');

      // 加载动态路由
      return initRouter().then((router) => {
        console.log(router);
        // 处理标签页缓存
        if (!useMultiTagsStoreHook().getMultiTagsCache) {
          const route = findRouteByPath(to.path, router.options.routes[0].children);
          getTopMenu(true);

          if (route && route.meta?.title) {
            const tagInfo = isAllEmpty(route.parentId) && route.meta?.backstage
              ? route.children[0]
              : route;

            useMultiTagsStoreHook().handleTags("push", {
              path: tagInfo.path,
              name: tagInfo.name,
              meta: tagInfo.meta,
            });
          }
        }

        // 修正路由名称为空的情况
        if (isAllEmpty(to.name)) {
          // 使用 replace 避免历史记录问题
          return next({ ...to, replace: true });
        }

        return toCorrectRoute();
      }).catch((err) => {
        console.error("路由初始化失败:", err);
        window.sessionStorage.removeItem('routeLoaded');
        return next("/error/404");
      });
      // }
    }
    // 正常导航
    return toCorrectRoute();
  } else {
    // 清除登录状态并重定向到登录页
    console.log('清除登录状态并重定向到登录页');
    removeToken();
    return next({ path: "/login" });
  }
});
router.afterEach(() => {
  NProgress.done();
});

export default router;
