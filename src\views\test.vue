<template>
  <div>
    <!-- 搜索栏位置 -->
    <div class="tbSearch">
      <el-form class="tbForm" :model="searchForm" label-width="70">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="输入框">
              <el-input v-model="searchForm.name" placeholder="请输入搜索内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="选择框">
              <el-select v-model="searchForm.region" placeholder="请选择你的选项">
                <el-option label="选项一" value="1" />
                <el-option label="选项二" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择时间">
              <el-col :span="11">
                <el-date-picker v-model="searchForm.date1" type="date" placeholder="日期选择框" style="width: 100%" />
              </el-col>
              <el-col :span="2" class="text-center">
                <span class="text-gray-500">-</span>
              </el-col>
              <el-col :span="11">
                <el-time-picker v-model="searchForm.date2" placeholder="时间选择框" style="width: 100%" />
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 搜索按钮 -->
        <div class="searchBtn">
          <el-button type="primary" @click="onSubmit">搜索</el-button>
        </div>
      </el-form>
    </div>
    <!-- 表格 -->
    <el-table class="tbBody" :data="tableData" :stripe="true" max-height="calc(100vh - 48px - 220px - 32px - 49px)">
      <el-table-column fixed prop="date" label="日期" width="150" />
      <el-table-column prop="name" label="姓名" width="120" />
      <el-table-column prop="state" label="状态" width="120" />
      <el-table-column prop="city" label="城市" width="120" />
      <el-table-column prop="address" label="地址" width="600" />
      <el-table-column prop="zip" label="编号" width="120" />
      <el-table-column fixed="right" label="操作" min-width="120">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="openMessageBox(scope.row)">
            删除
          </el-button>
          <el-button link type="primary" size="small" @click="ChangeShowModel(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 底部分页 -->
    <div class="demo-pagination-block">
      <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 50, 100, 200]" :disabled="false" :background="pagination.background"
        layout="total, sizes, prev, pager, next, jumper" :total="50">
        </el-pagination>
    </div>
       <!-- 弹出框 -->
    <el-dialog v-model="dialogVisible" title="详情信息" width="60%">
    <el-form :model="dialogForm">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="文本框">
            <el-input v-model="dialogForm.name" autocomplete="off" />
           </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="选择框">
        <el-select v-model="dialogForm.state" placeholder="请选择">
          <el-option label="选择一" value="1" />
          <el-option label="选择二" value="2" />
        </el-select>
      </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="文本框">
            <el-input v-model="dialogForm.city" autocomplete="off" />
           </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="选择时间:">
              <el-col :span="11">
                <el-date-picker v-model="dialogForm.time" type="date" placeholder="日期选择框" style="width: 100%" />
              </el-col>
              <el-col :span="2" class="text-center">
                <span class="text-gray-500">--</span>
              </el-col>
              <el-col :span="11">
                <el-time-picker v-model="dialogForm.time" placeholder="时间选择框" style="width: 100%" />
              </el-col>
            </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="文件上传:">
         <el-upload
         v-model:file-list="fileList"
         class="upload-demo"
         :action="uploadUrl"
         :on-change="handleChange"
         >
         <el-button type="primary">点击上传</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="文本框">
        <el-input v-model="dialogForm.zip"style="width: 240px":rows="2"type="textarea"placeholder=""/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
    
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus'
import { getTableData } from '@/api/test';
// 表格数据
const tableData = [
  {
    date: '2016-05-03',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Home',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Home',
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    state: 'California',
    city: 'Los Angeles',
    address: 'No. 189, Grove St, Los Angeles',
    zip: 'CA 90036',
    tag: 'Office',
  },
]
const params = {
  DeviceID: 1,
    Width: 300,
    Height: 400
}
const getData = async (resbody?: object) => { 
  const tb = await getTableData(resbody)
  console.log(tb);
}
getData(params);
// 搜索表单数据
const searchForm = reactive({
  name: '',
  region: '',
  date1: '',
  date2: '',
})
const dialogVisible = ref(false);
// 弹窗表单数据
const dialogForm = ref({
  data: '',
  name: '',
  state: "",
  city: "",
  time: "",
  address: '',
  zip: '',
  tag: ''
  
})
const fileList = ref([]);
const uploadUrl = ref('http')

const handleChange = ()=>{
  console.log('文件上传成功')
}

// 修改按钮点击后 弹出模态框
const ChangeShowModel = (row) => {
 console.log(JSON.stringify(row))
  dialogVisible.value = true;
}


// 搜索按钮
const onSubmit = () => {
  console.log('submit!');
}

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  background: true,
})

// 删除按钮点击后 弹出对话框
const openMessageBox = (row) => {
  console.log(JSON.stringify(row))
 ElMessageBox.confirm(
    '您确定要删除这条消息?',   // 提示信息
    '操作确认',   // 标题

    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 删除操作逻辑...


      // 删除成功后弹出提示信息
      ElMessage({
        type: 'success',
        message: '成功删除',
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消删除',
      })
    })
}


</script>

<style lang="scss" scoped>
.tbSearch {
  margin: 10px 0px;
  background-color: white;
  .tbForm {
    padding-top: 15px;
  }
}

.searchBtn {
  width: 150px;
  margin: 0 auto;

  .el-button {
    width: 100%;
    margin-bottom: 15px;
  }
}

// 分页样式调整
.demo-pagination-block {
  margin-right: 16px;
  margin-top: 16px;
  float: right;
}
</style>
