<template>
  <div class="cashier-system min-h-screen flex flex-col bg-gray-50">
    <!-- 顶部导航栏 -->
    <header
      class="bg-white/90 backdrop-blur-sm sticky top-0 z-50 border-b border-gray-200 shadow-sm"
    >
      <div class="container mx-auto px-4 sm:px-6">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center"
              >
                <font-awesome-icon icon="desktop" class="text-white text-xl" />
              </div>
              <div>
                <h1 class="text-xl font-bold text-gray-800">KTV 收银系统</h1>
                <p class="text-xs text-gray-500">智能房间管理</p>
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <!-- 搜索框 -->
            <el-input
              v-model="searchKeyword"
              placeholder="搜索房号..."
              class="w-48"
              size="default"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <font-awesome-icon icon="search" class="text-gray-400" />
              </template>
            </el-input>

            <!-- 返回系统选择 -->
            <el-button
              type="text"
              @click="backToSystemSelector"
              class="text-gray-600 hover:text-blue-600"
            >
              <font-awesome-icon icon="arrow-left" class="mr-1" />
              返回
            </el-button>

            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserCommand" trigger="click">
              <div
                class="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 rounded-lg px-3 py-2 transition-colors"
              >
                <el-avatar :size="32" class="bg-blue-500">
                  <font-awesome-icon icon="user" />
                </el-avatar>
                <span class="text-gray-700 font-medium">{{
                  currentUser.name
                }}</span>
                <font-awesome-icon icon="arrow-down" class="text-gray-400" />
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <font-awesome-icon icon="user" class="mr-2" />个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="password">
                    <font-awesome-icon icon="lock" class="mr-2" />修改密码
                  </el-dropdown-item>
                  <el-dropdown-item
                    divided
                    command="logout"
                    class="text-red-500"
                  >
                    <font-awesome-icon icon="power-off" class="mr-2" />退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex-grow container mx-auto p-4 sm:p-6">
      <div class="grid grid-cols-12 gap-6">
        <!-- 左侧信息栏 -->
        <aside class="col-span-12 lg:col-span-2 space-y-6">
          <!-- 状态总览 -->
          <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <h3 class="font-bold mb-4 text-gray-800 flex items-center">
              <font-awesome-icon icon="chart-bar" class="mr-2 text-blue-500" />
              状态总览
            </h3>
            <div class="grid grid-cols-2 gap-3 text-center">
              <div
                v-for="status in roomStatuses"
                :key="status.key"
                :class="[
                  'p-3 rounded-lg cursor-pointer transition-all duration-200 hover:scale-105',
                  getStatusCardClass(status.color)
                ]"
                @click="filterByStatus(status.key)"
              >
                <p class="text-2xl font-bold">{{ status.count }}</p>
                <p class="text-xs">{{ status.label }}</p>
              </div>
            </div>
          </div>

          <!-- 快捷操作 -->
          <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <h3 class="font-bold mb-3 text-gray-800 flex items-center">
              <font-awesome-icon icon="cogs" class="mr-2 text-green-500" />
              快捷操作
            </h3>
            <div class="space-y-2">
              <el-button
                v-for="action in quickActions"
                :key="action.key"
                class="w-full justify-start"
                text
                @click="handleQuickAction(action.key)"
              >
                <font-awesome-icon :icon="action.icon" class="mr-2" />
                {{ action.label }}
              </el-button>
            </div>
          </div>

          <!-- 最近开房 -->
          <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <h3 class="font-bold mb-4 text-gray-800 flex items-center">
              <font-awesome-icon icon="clock" class="mr-2 text-purple-500" />
              最近开房
            </h3>
            <div class="space-y-2">
              <div
                v-for="record in recentOpenings"
                :key="record.roomId"
                class="flex justify-between items-center p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div>
                  <div class="font-medium text-sm">{{ record.roomId }}</div>
                  <div class="text-xs text-gray-500">{{ record.roomType }}</div>
                </div>
                <div class="text-xs text-gray-600">{{ record.time }}</div>
              </div>
            </div>
          </div>

          <!-- 待处理预订 -->
          <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <h3 class="font-bold mb-3 text-gray-800 flex items-center">
              <font-awesome-icon icon="bell" class="mr-2 text-orange-500" />
              待处理预订
            </h3>
            <div class="text-center py-4">
              <font-awesome-icon
                icon="copy"
                class="text-3xl text-gray-300 mb-2"
              />
              <p class="text-sm text-gray-500">暂无即将开始的预订</p>
            </div>
          </div>
        </aside>

        <!-- 中间核心区 -->
        <div class="col-span-12 lg:col-span-7">
          <!-- 营业区域切换 -->
          <div
            class="flex items-center space-x-1 mb-6 bg-white p-1 rounded-xl shadow-sm border border-gray-100"
          >
            <el-button
              v-for="area in areas"
              :key="area.key"
              :type="currentArea === area.key ? 'primary' : ''"
              :plain="currentArea !== area.key"
              class="flex-1"
              size="default"
              @click="switchArea(area.key)"
            >
              {{ area.label }}
            </el-button>
          </div>

          <!-- 房间状态网格 -->
          <div class="space-y-6">
            <div
              v-for="area in filteredAreas"
              :key="area.key"
              class="bg-white rounded-xl p-6 shadow-sm border border-gray-100"
            >
              <!-- 区域标题 -->
              <div
                class="flex items-center justify-between mb-4 p-4 rounded-lg"
                :class="area.headerClass"
              >
                <div class="flex items-center space-x-3">
                  <div
                    class="w-3 h-3 rounded-full"
                    :class="area.dotColor"
                  ></div>
                  <h3 class="text-lg font-semibold" :class="area.textColor">
                    <font-awesome-icon :icon="area.icon" class="mr-2" />
                    {{ area.label }}
                  </h3>
                </div>
                <el-tag :type="area.tagType" size="small" round>
                  {{ area.rooms.length }} 间房
                </el-tag>
              </div>

              <!-- 房间网格 -->
              <div
                class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3"
              >
                <div
                  v-for="room in area.rooms"
                  :key="room.id"
                  :class="[
                    'room-card p-4 rounded-xl border-2 cursor-pointer transition-all duration-200',
                    getRoomStatusClass(room.status),
                    selectedRoom?.id === room.id
                      ? 'ring-2 ring-blue-500 ring-offset-2'
                      : '',
                    'hover:shadow-md hover:-translate-y-1'
                  ]"
                  @click="selectRoom(room)"
                >
                  <div class="text-center">
                    <div class="font-bold text-lg mb-1">{{ room.id }}</div>
                    <div class="text-xs text-gray-600 mb-2">
                      {{ room.type }}
                    </div>
                    <el-tag
                      :type="getStatusTagType(room.status)"
                      size="small"
                      round
                      class="text-xs"
                    >
                      {{ getStatusText(room.status) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧详情面板 -->
        <aside class="col-span-12 lg:col-span-3">
          <div
            class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 sticky top-24"
            style="
              height: calc(100vh - 8rem);
              max-height: 800px;
              min-height: 600px;
            "
          >
            <!-- 默认状态 -->
            <div
              v-if="!selectedRoom"
              class="flex flex-col items-center justify-center h-full text-center"
            >
              <div
                class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-4"
              >
                <font-awesome-icon
                  icon="mouse-pointer"
                  class="text-3xl text-gray-400"
                />
              </div>
              <h4 class="text-lg font-medium text-gray-600 mb-2">选择房间</h4>
              <p class="text-gray-500 text-sm">请点击左侧房间查看详细信息</p>
            </div>

            <!-- 选中房间详情 -->
            <div v-else class="flex flex-col h-full space-y-4">
              <!-- 房间信息头部 -->
              <div class="flex-shrink-0 border-b border-gray-200 pb-4">
                <div class="flex justify-between items-start mb-3">
                  <div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-1">
                      房间 {{ selectedRoom.id }}
                    </h3>
                    <p class="text-sm text-gray-600">
                      {{ selectedRoom.type }}
                    </p>
                  </div>
                  <el-tag
                    :type="getStatusTagType(selectedRoom.status)"
                    size="large"
                  >
                    {{ getStatusText(selectedRoom.status) }}
                  </el-tag>
                </div>
              </div>

              <!-- 房间详细信息 -->
              <div
                v-if="selectedRoom.status === 'occupied'"
                class="flex-shrink-0 space-y-3"
              >
                <div class="bg-blue-50 rounded-lg p-3">
                  <div class="grid grid-cols-2 gap-3 text-sm">
                    <div>
                      <span class="text-gray-600">开房时间:</span>
                      <div class="font-medium">
                        {{ selectedRoom.startTime }}
                      </div>
                    </div>
                    <div>
                      <span class="text-gray-600">顾客:</span>
                      <div class="font-medium">{{ selectedRoom.customer }}</div>
                    </div>
                    <div>
                      <span class="text-gray-600">已用时长:</span>
                      <div class="font-medium text-blue-600">
                        {{ selectedRoom.duration }}
                      </div>
                    </div>
                    <div>
                      <span class="text-gray-600">当前费用:</span>
                      <div class="font-medium text-green-600">
                        ¥{{ selectedRoom.total }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 消费明细 -->
              <div class="flex-grow overflow-hidden flex flex-col">
                <h4
                  class="text-sm font-medium text-gray-600 mb-3 flex items-center"
                >
                  <font-awesome-icon icon="list" class="mr-2" />
                  消费明细
                </h4>
                <div class="flex-grow overflow-y-auto custom-scrollbar">
                  <div
                    v-if="selectedRoom.items && selectedRoom.items.length > 0"
                    class="space-y-2"
                  >
                    <div
                      v-for="item in selectedRoom.items"
                      :key="item.id"
                      class="flex justify-between items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div class="flex-1">
                        <div class="font-medium text-sm">{{ item.name }}</div>
                        <div class="text-gray-500 text-xs">
                          {{ item.quantity }}x ¥{{ item.price }}
                        </div>
                      </div>
                      <div class="font-medium text-green-600">
                        ¥{{ item.total }}
                      </div>
                    </div>
                  </div>
                  <div v-else class="text-center py-8">
                    <font-awesome-icon
                      icon="shopping-cart"
                      class="text-3xl text-gray-300 mb-2"
                    />
                    <p class="text-sm text-gray-500">暂无消费记录</p>
                  </div>
                </div>
              </div>

              <!-- 总计和操作按钮 -->
              <div class="flex-shrink-0 pt-4 border-t border-gray-200">
                <div
                  class="flex justify-between items-center mb-4 p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg"
                >
                  <span class="text-gray-700 font-medium">总计金额</span>
                  <span class="text-2xl font-bold text-green-600">
                    ¥{{ selectedRoom.total || 0 }}
                  </span>
                </div>
                <div class="space-y-2">
                  <el-button
                    v-for="action in getRoomActions(selectedRoom.status)"
                    :key="action.key"
                    :type="action.type"
                    class="w-full"
                    size="large"
                    @click="handleRoomAction(action.key, selectedRoom)"
                  >
                    <font-awesome-icon :icon="action.icon" class="mr-2" />
                    {{ action.label }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </aside>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
// 不再需要导入 Element Plus 图标，使用 FontAwesome 替代
import { removeToken } from "@/utils/auth";

defineOptions({
  name: "CashierIndex"
});

const router = useRouter();

// 当前用户信息
const currentUser = ref({
  name: "收银员",
  role: "cashier"
});

// 响应式数据
const searchKeyword = ref("");
const currentArea = ref("all");
const selectedRoom = ref<Room | null>(null);

// 房间状态定义
const roomStatuses = reactive([
  { key: "occupied", label: "占用", color: "orange", count: 0 },
  { key: "free", label: "可用", color: "green", count: 0 },
  { key: "cleaning", label: "待清", color: "red", count: 0 },
  { key: "booked", label: "预定", color: "blue", count: 0 },
  { key: "maintenance", label: "维修", color: "purple", count: 0 },
  { key: "disabled", label: "停用", color: "gray", count: 0 }
]);

// 快捷操作
const quickActions = [
  { key: "booking", label: "预订", icon: "plus" },
  { key: "member", label: "会员", icon: "users" },
  { key: "report", label: "报表", icon: "chart-line" }
];

// 区域定义
const areas = [
  { key: "all", label: "全部" },
  { key: "V", label: "VIP区" },
  { key: "M", label: "中包区" },
  { key: "S", label: "小包区" },
  { key: "X", label: "大包区" }
];

// 最近开房记录
const recentOpenings = reactive([
  { roomId: "V01", roomType: "VIP套房", time: "15:12" },
  { roomId: "S02", roomType: "小包", time: "14:58" },
  { roomId: "M01", roomType: "中包", time: "14:20" },
  { roomId: "V05", roomType: "VIP套房", time: "13:45" },
  { roomId: "M08", roomType: "中包", time: "13:11" }
]);

// 房间数据类型定义
interface Room {
  id: string;
  type: string;
  status:
    | "occupied"
    | "free"
    | "cleaning"
    | "booked"
    | "maintenance"
    | "disabled";
  area: string;
  startTime?: string;
  customer?: string;
  duration?: string;
  total?: number;
  items?: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    total: number;
  }>;
}

// 模拟房间数据
const allRooms = reactive<Room[]>([
  // VIP区
  {
    id: "V01",
    type: "VIP套房",
    status: "occupied",
    area: "V",
    startTime: "15:12",
    customer: "张先生",
    duration: "2小时30分",
    total: 580,
    items: [
      { id: "1", name: "房费", quantity: 1, price: 300, total: 300 },
      { id: "2", name: "啤酒", quantity: 6, price: 30, total: 180 },
      { id: "3", name: "果盘", quantity: 1, price: 100, total: 100 }
    ]
  },
  { id: "V02", type: "VIP套房", status: "free", area: "V" },
  { id: "V03", type: "VIP套房", status: "booked", area: "V" },
  { id: "V04", type: "VIP套房", status: "cleaning", area: "V" },
  {
    id: "V05",
    type: "VIP套房",
    status: "occupied",
    area: "V",
    startTime: "13:45",
    customer: "李女士",
    duration: "4小时15分",
    total: 720
  },

  // 中包区
  {
    id: "M01",
    type: "中包",
    status: "occupied",
    area: "M",
    startTime: "14:20",
    customer: "王先生",
    duration: "3小时",
    total: 450
  },
  { id: "M02", type: "中包", status: "free", area: "M" },
  { id: "M03", type: "中包", status: "free", area: "M" },
  { id: "M04", type: "中包", status: "maintenance", area: "M" },
  { id: "M05", type: "中包", status: "free", area: "M" },
  { id: "M06", type: "中包", status: "free", area: "M" },
  { id: "M07", type: "中包", status: "free", area: "M" },
  {
    id: "M08",
    type: "中包",
    status: "occupied",
    area: "M",
    startTime: "13:11",
    customer: "陈先生",
    duration: "4小时30分",
    total: 380
  },

  // 小包区
  { id: "S01", type: "小包", status: "free", area: "S" },
  {
    id: "S02",
    type: "小包",
    status: "occupied",
    area: "S",
    startTime: "14:58",
    customer: "刘先生",
    duration: "2小时45分",
    total: 280
  },
  { id: "S03", type: "小包", status: "free", area: "S" },
  { id: "S04", type: "小包", status: "free", area: "S" },
  { id: "S05", type: "小包", status: "cleaning", area: "S" },
  { id: "S06", type: "小包", status: "free", area: "S" },
  { id: "S07", type: "小包", status: "free", area: "S" },
  { id: "S08", type: "小包", status: "free", area: "S" },

  // 大包区
  { id: "X01", type: "大包", status: "free", area: "X" },
  { id: "X02", type: "大包", status: "free", area: "X" },
  { id: "X03", type: "大包", status: "disabled", area: "X" },
  { id: "X04", type: "大包", status: "free", area: "X" }
]);

// 计算属性
const filteredAreas = computed(() => {
  const areaConfigs = [
    {
      key: "V",
      label: "VIP区",
      icon: "crown",
      headerClass:
        "bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-100",
      textColor: "text-purple-600",
      dotColor: "bg-gradient-to-r from-purple-500 to-pink-500",
      tagType: "danger"
    },
    {
      key: "M",
      label: "中包区",
      icon: "users",
      headerClass:
        "bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-100",
      textColor: "text-blue-600",
      dotColor: "bg-gradient-to-r from-blue-500 to-cyan-500",
      tagType: "primary"
    },
    {
      key: "S",
      label: "小包区",
      icon: "user-circle",
      headerClass:
        "bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100",
      textColor: "text-green-600",
      dotColor: "bg-gradient-to-r from-green-500 to-emerald-500",
      tagType: "success"
    },
    {
      key: "X",
      label: "大包区",
      icon: "star",
      headerClass:
        "bg-gradient-to-r from-orange-50 to-red-50 border border-orange-100",
      textColor: "text-orange-600",
      dotColor: "bg-gradient-to-r from-orange-500 to-red-500",
      tagType: "warning"
    }
  ];

  return areaConfigs
    .map(config => ({
      ...config,
      rooms: allRooms.filter(
        room =>
          (currentArea.value === "all" || room.area === config.key) &&
          (searchKeyword.value === "" ||
            room.id.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      )
    }))
    .filter(
      area => currentArea.value === "all" || area.key === currentArea.value
    );
});

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

const backToSystemSelector = () => {
  router.push("/system-selector");
};

const handleUserCommand = (command: string) => {
  switch (command) {
    case "profile":
      ElMessage.info("个人资料功能开发中...");
      break;
    case "password":
      ElMessage.info("修改密码功能开发中...");
      break;
    case "logout":
      ElMessageBox.confirm("确定要退出登录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        removeToken();
        router.push("/login");
        ElMessage.success("已退出登录");
      });
      break;
  }
};

const filterByStatus = (status: string) => {
  ElMessage.info(`筛选${status}状态的房间`);
};

const switchArea = (area: string) => {
  currentArea.value = area;
  selectedRoom.value = null;
};

const selectRoom = (room: Room) => {
  selectedRoom.value = room;
};

const handleQuickAction = (action: string) => {
  switch (action) {
    case "booking":
      ElMessage.info("预订功能开发中...");
      break;
    case "member":
      ElMessage.info("会员管理功能开发中...");
      break;
    case "report":
      ElMessage.info("报表功能开发中...");
      break;
  }
};

const getStatusCardClass = (color: string) => {
  const classes = {
    orange: "bg-orange-50 text-orange-600 hover:bg-orange-100",
    green: "bg-green-50 text-green-600 hover:bg-green-100",
    red: "bg-red-50 text-red-600 hover:bg-red-100",
    blue: "bg-blue-50 text-blue-600 hover:bg-blue-100",
    purple: "bg-purple-50 text-purple-600 hover:bg-purple-100",
    gray: "bg-gray-50 text-gray-600 hover:bg-gray-100"
  };
  return classes[color] || classes.gray;
};

const getRoomStatusClass = (status: string) => {
  const statusClasses = {
    occupied:
      "bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100",
    free: "bg-green-50 border-green-200 text-green-800 hover:bg-green-100",
    cleaning: "bg-red-50 border-red-200 text-red-800 hover:bg-red-100",
    booked: "bg-blue-50 border-blue-200 text-blue-800 hover:bg-blue-100",
    maintenance:
      "bg-purple-50 border-purple-200 text-purple-800 hover:bg-purple-100",
    disabled: "bg-gray-50 border-gray-200 text-gray-800 hover:bg-gray-100"
  };
  return statusClasses[status] || statusClasses.free;
};

const getStatusText = (status: string) => {
  const statusTexts = {
    occupied: "占用中",
    free: "可用",
    cleaning: "待清洁",
    booked: "已预订",
    maintenance: "维修中",
    disabled: "已停用"
  };
  return statusTexts[status] || "未知";
};

const getStatusTagType = (status: string) => {
  const tagTypes = {
    occupied: "warning",
    free: "success",
    cleaning: "danger",
    booked: "primary",
    maintenance: "",
    disabled: "info"
  };
  return tagTypes[status] || "";
};

const getRoomActions = (status: string) => {
  const actions = {
    free: [
      { key: "checkin", label: "开房", type: "primary", icon: "clock" },
      { key: "book", label: "预订", type: "", icon: "plus" }
    ],
    occupied: [
      { key: "checkout", label: "结账", type: "success", icon: "money-bill" },
      { key: "addItem", label: "加单", type: "", icon: "plus" },
      { key: "transfer", label: "换房", type: "", icon: "cog" }
    ],
    booked: [
      { key: "checkin", label: "入住", type: "primary", icon: "clock" },
      { key: "cancel", label: "取消预订", type: "danger", icon: "cog" }
    ],
    cleaning: [
      { key: "cleaned", label: "清洁完成", type: "success", icon: "cog" }
    ],
    maintenance: [
      { key: "repaired", label: "维修完成", type: "success", icon: "cog" }
    ],
    disabled: [
      { key: "enable", label: "启用房间", type: "primary", icon: "cog" }
    ]
  };
  return actions[status] || [];
};

const handleRoomAction = (action: string, room: Room) => {
  ElMessage.info(`执行操作: ${action} - 房间: ${room.id}`);
};

const updateStatusCounts = () => {
  roomStatuses.forEach(status => {
    status.count = allRooms.filter(room => room.status === status.key).length;
  });
};

onMounted(() => {
  updateStatusCounts();
});
</script>

<style scoped>
/* 收银系统专用样式 */
.cashier-system {
  font-family: "Noto Sans SC", sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 房间卡片样式 */
.room-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.room-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s;
}

.room-card:hover::before {
  left: 100%;
}

.room-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 状态指示器动画 */
.status-indicator {
  position: relative;
}

.status-indicator.occupied::after {
  content: "";
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #f59e0b;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

/* 区域标题样式 */
.area-header {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.4)
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片悬停效果 */
.bg-white {
  transition: all 0.3s ease;
}

.bg-white:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 按钮样式增强 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-1px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .grid {
    gap: 1rem;
  }

  .room-card {
    padding: 0.75rem;
  }
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Element Plus 组件样式覆盖 */
.el-input__wrapper {
  border-radius: 8px;
}

.el-tag {
  border-radius: 6px;
  font-weight: 500;
}

.el-avatar {
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* 顶部导航栏样式 */
header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* 主内容区域 */
main {
  background: transparent;
}
</style>
