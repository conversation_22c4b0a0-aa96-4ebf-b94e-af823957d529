<!DOCTYPE html>
<html lang="zh-CN" class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>KTV 智能收银系统 - 设计稿</title>

    <!-- 技术栈 CDN 引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap"
      rel="stylesheet"
    />

    <!-- 链接到新的CSS文件 -->
    <link rel="stylesheet" href="style.css" />
  </head>

  <body class="antialiased">
    <div class="min-h-screen flex flex-col">
      <!-- 1. 顶部导航栏 -->
      <header
        class="bg-[var(--bg-light)]/80 backdrop-blur-sm sticky top-0 z-50 border-b border-[var(--border-color)]"
      >
        <div class="container mx-auto px-4 sm:px-6">
          <div class="flex items-center justify-between h-14">
            <div class="flex items-center space-x-3">
              <i class="fas fa-satellite-dish highlight-text text-2xl"></i>
              <h1 class="text-lg font-bold">KTV 收银系统</h1>
            </div>
            <div class="flex items-center space-x-3">
              <input
                type="text"
                id="room-search-input"
                placeholder="搜索房号..."
                class="px-3 py-1.5 rounded-lg border border-[var(--border-color)] bg-[var(--bg-light)] text-sm focus:ring-1 focus:ring-[var(--highlight-color)] focus:border-[var(--highlight-color)] transition-shadow shadow-sm w-40"
              />
              <button
                id="theme-toggle"
                class="h-9 w-9 rounded-full flex items-center justify-center text-[var(--text-secondary)] hover:bg-black/5 dark:hover:bg-white/10"
              >
                <i class="fas fa-sun hidden" id="theme-icon-sun"></i>
                <i class="fas fa-moon" id="theme-icon-moon"></i>
              </button>

              <div class="relative" id="user-menu-button-container">
                <button
                  type="button"
                  class="h-9 w-9 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center"
                  id="user-menu-button"
                  aria-expanded="false"
                  aria-haspopup="true"
                  onclick="document.getElementById('user-menu-dropdown').classList.toggle('hidden')"
                >
                  <i class="fas fa-user text-gray-600 dark:text-gray-300"></i>
                </button>
                <!-- Dropdown menu -->
                <div
                  id="user-menu-dropdown"
                  class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-[var(--bg-light)] ring-1 ring-black ring-opacity-5 focus:outline-none border border-[var(--border-color)] z-50"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="user-menu-button"
                >
                  <a
                    href="#"
                    class="block px-4 py-2 text-sm text-[var(--text-primary)] hover:bg-black/5 dark:hover:bg-white/10"
                    role="menuitem"
                    >个人资料</a
                  >
                  <a
                    href="#"
                    class="block px-4 py-2 text-sm text-[var(--text-primary)] hover:bg-black/5 dark:hover:bg-white/10"
                    role="menuitem"
                    >修改密码</a
                  >
                  <hr class="border-[var(--border-color)] my-1" />
                  <a
                    href="#"
                    class="block px-4 py-2 text-sm text-red-500 hover:bg-red-500/10"
                    role="menuitem"
                    >退出登录</a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- 2. 主内容区域 -->
      <main class="flex-grow container mx-auto p-4 sm:p-6">
        <div class="grid grid-cols-12 gap-6">
          <!-- 2.1 左侧信息栏 -->
          <aside class="col-span-12 lg:col-span-2 space-y-6">
            <!-- 状态总览 -->
            <div class="card rounded-xl p-4 animate-on-scroll">
              <h3 class="font-bold mb-4">状态总览</h3>
              <div class="grid grid-cols-2 gap-3 text-center">
                <div
                  class="status-filter-item bg-orange-500/10 text-orange-400 p-2 rounded-lg cursor-pointer hover:ring-2 hover:ring-orange-400/50 transition-all"
                  data-status="occupied"
                >
                  <p id="stats-occupied" class="text-2xl font-bold">0</p>
                  <p class="text-xs">占用</p>
                </div>
                <div
                  class="status-filter-item bg-green-500/10 text-green-400 p-2 rounded-lg cursor-pointer hover:ring-2 hover:ring-green-400/50 transition-all"
                  data-status="free"
                >
                  <p id="stats-free" class="text-2xl font-bold">0</p>
                  <p class="text-xs">可用</p>
                </div>
                <div
                  class="status-filter-item bg-red-500/10 text-red-400 p-2 rounded-lg cursor-pointer hover:ring-2 hover:ring-red-400/50 transition-all"
                  data-status="cleaning"
                >
                  <p id="stats-cleaning" class="text-2xl font-bold">0</p>
                  <p class="text-xs">待清</p>
                </div>
                <div
                  class="status-filter-item bg-blue-500/10 text-blue-400 p-2 rounded-lg cursor-pointer hover:ring-2 hover:ring-blue-400/50 transition-all"
                  data-status="booked"
                >
                  <p id="stats-booked" class="text-2xl font-bold">0</p>
                  <p class="text-xs">预定</p>
                </div>
                <div
                  class="status-filter-item bg-purple-500/10 text-purple-400 p-2 rounded-lg cursor-pointer hover:ring-2 hover:ring-purple-400/50 transition-all"
                  data-status="maintenance"
                >
                  <p id="stats-maintenance" class="text-2xl font-bold">0</p>
                  <p class="text-xs">维修</p>
                </div>
                <div
                  class="status-filter-item bg-gray-500/10 text-gray-400 p-2 rounded-lg cursor-pointer hover:ring-2 hover:ring-gray-400/50 transition-all"
                  data-status="disabled"
                >
                  <p id="stats-disabled" class="text-2xl font-bold">0</p>
                  <p class="text-xs">停用</p>
                </div>
              </div>
            </div>

            <!-- 快捷操作 -->
            <div class="card rounded-xl p-4 animate-on-scroll">
              <h3 class="font-bold mb-3">快捷操作</h3>
              <div class="space-y-2 text-sm">
                <button
                  class="w-full flex items-center justify-start space-x-2 py-2 px-3 rounded-md hover:bg-[var(--bg-dark)] transition-colors"
                >
                  <i
                    class="fas fa-plus-square fa-fw w-4 text-[var(--text-secondary)]"
                  ></i>
                  <span>预订</span>
                </button>
                <button
                  class="w-full flex items-center justify-start space-x-2 py-2 px-3 rounded-md hover:bg-[var(--bg-dark)] transition-colors"
                >
                  <i
                    class="fas fa-users fa-fw w-4 text-[var(--text-secondary)]"
                  ></i>
                  <span>会员</span>
                </button>
                <button
                  class="w-full flex items-center justify-start space-x-2 py-2 px-3 rounded-md hover:bg-[var(--bg-dark)] transition-colors"
                >
                  <i
                    class="fas fa-chart-line fa-fw w-4 text-[var(--text-secondary)]"
                  ></i>
                  <span>报表</span>
                </button>
              </div>
            </div>
            
            <!-- 最近开房 -->
            <div class="card rounded-xl p-4 animate-on-scroll">
              <h3 class="font-bold mb-4">最近开房</h3>
              <div class="overflow-x-auto">
                <table class="w-full text-xs text-left">
                  <thead class="text-[var(--text-secondary)]">
                    <tr>
                      <th class="pb-2 font-normal">房号</th>
                      <th class="pb-2 font-normal">类别</th>
                      <th class="pb-2 font-normal text-right">时间</th>
                    </tr>
                  </thead>
                  <tbody id="recent-openings-table">
                    <tr>
                      <td class="px-2 py-1">V01</td>
                      <td class="px-2 py-1">VIP套房</td>
                      <td class="px-2 py-1 text-right">15:12</td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1">S02</td>
                      <td class="px-2 py-1">小包</td>
                      <td class="px-2 py-1 text-right">14:58</td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1">M01</td>
                      <td class="px-2 py-1">中包</td>
                      <td class="px-2 py-1 text-right">14:20</td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1">V05</td>
                      <td class="px-2 py-1">VIP套房</td>
                      <td class="px-2 py-1 text-right">13:45</td>
                    </tr>
                    <tr>
                      <td class="px-2 py-1">M08</td>
                      <td class="px-2 py-1">中包</td>
                      <td class="px-2 py-1 text-right">13:11</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 待处理预订 -->
            <div class="card rounded-xl p-4 animate-on-scroll">
              <h3 class="font-bold mb-3">待处理预订</h3>
              <div id="upcoming-bookings-list" class="space-y-2 text-xs">
                <!-- JS动态填充 -->
                <p class="text-[var(--text-secondary)] text-center py-2">
                  暂无即将开始的预订
                </p>
              </div>
            </div>
          </aside>

          <!-- 2.2 中间核心区 -->
          <div class="col-span-12 lg:col-span-7">
            <!-- 营业区域切换 -->
            <div
              class="flex items-center space-x-1 mb-4 animate-on-scroll card p-1 rounded-lg"
            >
              <button
                data-area="all"
                class="area-tab flex-1 text-sm py-2 px-3 rounded-md transition-colors"
              >
                全部
              </button>
              <button
                data-area="V"
                class="area-tab flex-1 text-sm py-2 px-3 rounded-md transition-colors"
              >
                VIP区
              </button>
              <button
                data-area="M"
                class="area-tab flex-1 text-sm py-2 px-3 rounded-md transition-colors"
              >
                中包区
              </button>
              <button
                data-area="S"
                class="area-tab flex-1 text-sm py-2 px-3 rounded-md transition-colors"
              >
                小包区
              </button>
            </div>

            <!-- 房间状态网格 - 按区域分组显示 -->
            <div id="room-grid-container" class="space-y-6 animate-on-scroll">
              <!-- VIP区 -->
              <div id="area-V" class="area-section">
                <div class="area-header flex items-center justify-between mb-3 p-3 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/20 dark:border-purple-500/20">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"></div>
                    <h3 class="text-lg font-semibold text-purple-600 dark:text-purple-400">
                      <i class="fas fa-crown mr-2"></i>VIP区
                    </h3>
                  </div>
                  <span id="area-V-count" class="text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30 px-2 py-1 rounded-full">
                    0 间房
                  </span>
                </div>
                <div id="area-V-rooms" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                  <!-- VIP区房间将在这里动态填充 -->
                </div>
              </div>

              <!-- 中包区 -->
              <div id="area-M" class="area-section">
                <div class="area-header flex items-center justify-between mb-3 p-3 rounded-lg bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-200/20 dark:border-blue-500/20">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500"></div>
                    <h3 class="text-lg font-semibold text-blue-600 dark:text-blue-400">
                      <i class="fas fa-users mr-2"></i>中包区
                    </h3>
                  </div>
                  <span id="area-M-count" class="text-sm text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-full">
                    0 间房
                  </span>
                </div>
                <div id="area-M-rooms" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                  <!-- 中包区房间将在这里动态填充 -->
                </div>
              </div>

              <!-- 小包区 -->
              <div id="area-S" class="area-section">
                <div class="area-header flex items-center justify-between mb-3 p-3 rounded-lg bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-200/20 dark:border-green-500/20">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full bg-gradient-to-r from-green-500 to-emerald-500"></div>
                    <h3 class="text-lg font-semibold text-green-600 dark:text-green-400">
                      <i class="fas fa-user-friends mr-2"></i>小包区
                    </h3>
                  </div>
                  <span id="area-S-count" class="text-sm text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">
                    0 间房
                  </span>
                </div>
                <div id="area-S-rooms" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                  <!-- 小包区房间将在这里动态填充 -->
                </div>
              </div>

              <!-- 大包区 -->
              <div id="area-X" class="area-section">
                <div class="area-header flex items-center justify-between mb-3 p-3 rounded-lg bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-200/20 dark:border-orange-500/20">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full bg-gradient-to-r from-orange-500 to-red-500"></div>
                    <h3 class="text-lg font-semibold text-orange-600 dark:text-orange-400">
                      <i class="fas fa-star mr-2"></i>大包区
                    </h3>
                  </div>
                  <span id="area-X-count" class="text-sm text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30 px-2 py-1 rounded-full">
                    0 间房
                  </span>
                </div>
                <div id="area-X-rooms" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                  <!-- 大包区房间将在这里动态填充 -->
                </div>
              </div>

              <!-- 传统房间网格（用于"全部"视图和搜索结果） -->
              <div id="room-grid" class="hidden grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                <!-- JS动态填充 -->
              </div>
            </div>
          </div>

          <!-- 2.3 右侧详情面板 -->
          <aside class="col-span-12 lg:col-span-3">
            <div
              class="card rounded-xl p-4 flex flex-col sticky top-20 animate-on-scroll lg:w-[360px]"
              style="height: calc(100vh - 6rem); max-height: 800px; min-height: 600px;"
            >
              <!-- 默认状态 -->
              <div
                id="details-default"
                class="flex flex-col items-center justify-center h-full text-center"
              >
                <i
                  class="fas fa-mouse-pointer text-4xl text-[var(--text-secondary)]"
                ></i>
                <p class="mt-4 text-[var(--text-secondary)]">
                  请选择一个房间查看详情
                </p>
              </div>

              <!-- 选中房间后显示的内容 (模板) -->
              <div
                id="details-content"
                class="hidden flex flex-col h-full space-y-3"
              >
                <!-- Section 1: Room Header (ID, Type, Status) -->
                <div
                  class="flex-shrink-0 border-b border-[var(--border-color)] pb-3"
                >
                  <div class="flex justify-between items-center mb-1">
                    <h3 class="text-xl font-bold">
                      房间 <span id="details-room-id"></span>
                    </h3>
                    <span
                      id="details-room-status-badge"
                      class="px-2.5 py-0.5 text-xs font-semibold rounded-full capitalize"
                    ></span>
                  </div>
                  <p class="text-sm text-[var(--text-secondary)]">
                    类型:
                    <span
                      id="details-room-type"
                      class="font-medium text-[var(--text-primary)]"
                    ></span>
                  </p>
                </div>

                <!-- Section 2: Additional Room Info (Start Time, Customer, etc.) -->
                <div
                  id="details-room-meta"
                  class="flex-shrink-0 text-sm space-y-1.5"
                >
                  <!-- JS动态填充: 开房时间, 顾客, 预订时间等 -->
                </div>

                <!-- Section 3: Item List (Scrollable) -->
                <div
                  class="flex-grow overflow-y-auto pr-1 custom-scrollbar"
                  style="min-height: 100px"
                >
                  <h4
                    class="text-sm font-medium text-[var(--text-secondary)] mb-2 sticky top-0 bg-[var(--bg-light)] py-1"
                  >
                    消费明细
                  </h4>
                  <ul id="details-item-list" class="space-y-2 text-sm"></ul>
                </div>

                <!-- Section 4: Summary and Actions -->
                <div
                  class="flex-shrink-0 pt-3 border-t border-[var(--border-color)]"
                >
                  <div class="flex justify-between items-center mb-3">
                    <span class="text-[var(--text-secondary)]">总计</span>
                    <span id="details-total" class="text-2xl font-bold"></span>
                  </div>
                  <div id="room-action-buttons">
                    <!-- 按钮将根据房间状态动态生成 -->
                  </div>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </main>
    </div>

    <!-- 链接到JS文件 -->
    <script type="module" src="main.js"></script>
  </body>
</html>
