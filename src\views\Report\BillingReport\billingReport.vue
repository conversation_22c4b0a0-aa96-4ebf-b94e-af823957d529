<script setup>
import { ref } from "vue";
import { Search, Upload } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import axios from "axios";
import { onMounted, computed } from "vue";
// 顶部选择器与时间栏绑定数据
const tableData = ref([]);
const tableLoad = ref(false); // 控制表格加载状态
const timeSlotsData = computed(() => {
  if (!tableData.value.length) return [];

  const allSlots = {};
  const allBaseData = [];

  // 处理所有数据
  tableData.value.forEach(item => {
    const slots = {};
    const baseData = {};

    // 分离时间段数据和其他数据
    Object.keys(item).forEach(key => {
      const timeMatch = key.match(/^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$/);

      if (timeMatch) {
        const [_, timeSlot, field] = timeMatch;
        if (!slots[timeSlot]) {
          slots[timeSlot] = {};
        }
        slots[timeSlot][field] = item[key];
      } else {
        baseData[key] = item[key];
      }
    });

    // 将当前条目的数据保存
    allBaseData.push(baseData);

    // 合并时间段数据
    Object.keys(slots).forEach(timeSlot => {
      if (!allSlots[timeSlot]) {
        allSlots[timeSlot] = [];
      }
      allSlots[timeSlot].push(slots[timeSlot]);
    });
  });

  // console.log("所有基础数据:", allBaseData);
  return {
    baseData: allBaseData, // 现在包含所有条目的基础数据
    timeSlots: Object.keys(allSlots).map(timeSlot => ({
      timeSlot,
      data: allSlots[timeSlot] // 每个时间段包含所有条目的数据
    }))
  };
});
// 获取前一天日期
const getTodayRange = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate() - 1).padStart(2, "0");
  const todayStr = `${year}${month}${day}`;
  return [todayStr, todayStr]; // [YYYYMMDD, YYYYMMDD]
};
const select = ref({
  store: "",
  time: getTodayRange()
});

//  选择器数据
const options = ref([]);

// 获取表格数据
async function fetchReport(shopId, beginDate, endDate) {
  try {
    const response = await axios.get("http://183.63.130.69:88/ExecUse/index", {
      params: {
        Ex: "OperateData.dbo.usp_GenerateDynamicUnifiedDailyReport",
        ShopId: shopId,
        BeginDate: beginDate,
        EndDate: endDate
      },
      timeout: 5000
    });

    // 处理带括号的JSON响应
    let responseData = response.data;
    if (typeof responseData === "string" && responseData.startsWith("(")) {
      responseData = responseData.replace(/^\((.*)\)$/, "$1");
      return JSON.parse(responseData);
    }
    return responseData;
  } catch (error) {
    console.error("API请求失败:", error);
    throw error;
  }
}
function groupByTimeSlot(data) {
  const result = {};
  // 遍历原始数据中的每个对象
  data.ObjceData.forEach(item => {
    // 获取所有时间段（通过匹配包含时间段的键名）
    const timeSlots = Object.keys(item)
      .filter(key => key.match(/^\d{2}:\d{2}-\d{2}:\d{2}_/))
      .map(key => key.split("_")[0]);

    // 去重获取唯一时间段
    const uniqueTimeSlots = [...new Set(timeSlots)];

    // 为每个时间段创建分组对象
    uniqueTimeSlots.forEach(slot => {
      if (!result[slot]) {
        result[slot] = {};
      }

      // 提取该时间段的所有属性
      Object.keys(item).forEach(key => {
        if (key.startsWith(slot + "_")) {
          const newKey = key.replace(slot + "_", "");
          result[slot][newKey] = item[key];
        }
      });
    });
  });
  console.log("分组后的数据:", result);
  return result;
}

// 提取时间段并重组数据

onMounted(async () => {
  try {
    const response = await axios.get("http://183.63.130.69:200/ExecUse/Index", {
      params: {
        Ex: "GrouponBase.dbo.Ex_SelTable",
        TableName: "Mims.dbo.ShopInfo"
      }
    });
    let responseData = response.data;
    // console.log("API返回数据:", responseData);
    if (typeof responseData === "string" && responseData.startsWith("(")) {
      responseData = JSON.parse(responseData.replace(/^\((.*)\)$/, "$1"));
    }
    options.value = responseData.ObjceData.filter(
      shop =>
        shop.IsUse && shop.ShopName !== "清远店" && shop.ShopName !== "黄岐店"
    ) // 只保留 IsUse 为 true 的店铺
      .map(shop => ({
        value: shop.ShopID, // 使用 ShopID 作为 value
        label: shop.ShopName // 使用 ShopName 作为 label
      }));
  } catch (err) {
    console.error("初始化失败:", err);
  }
});

// 时间格式化
const formatDate = (row, column, cellValue) => {
  if (column.property === "ReportDate") {
    // 处理 /Date(1753286400000)/ 格式
    const timestamp = parseInt(cellValue.match(/\d+/)[0]);
    return new Date(timestamp).toLocaleDateString();
  }
  return cellValue;
};

// 搜索
const search = async () => {
  if (!select.value.store || select.value.time.length !== 2) {
    ElMessage({
      showClose: true,
      message: "请选择门店和完整的时间范围！",
      type: "error"
    });
    return;
  }
  try {
    tableLoad.value = true;
    const data = await fetchReport(
      select.value.store,
      select.value.time[0],
      select.value.time[1]
    );
    // console.log("API返回数据:", data);
    groupByTimeSlot(data);
    tableData.value = data.ObjceData || []; // 确保是数组类型，避免空值问题
    tableLoad.value = false;
    if (tableData.value.length === 0) {
      ElMessage("当前选择的日期没有查询到数据！");
    }
    // console.log("重组后的数据:", timeSlotsData.value);
  } catch (err) {
    tableLoad.value = false;
    console.error("查询失败:", err);
  }
};

// 导出
const isExporting = ref(false); // 控制导出按钮的加载状态
const derived = async () => {
  if (!select.value.store || select.value.time.length !== 2) {
    ElMessage({
      showClose: true,
      message: "请选择门店和完整的时间范围！",
      type: "error"
    });
    return;
  }
  try {
    // 验证参数
    isExporting.value = true;
    const response = await axios.get(
      "http://183.63.130.69:88/ExportToCsv/export",
      {
        params: {
          Ex: "OperateData.dbo.usp_GenerateDynamicUnifiedDailyReport",
          ShopId: select.value.store,
          BeginDate: select.value.time[0],
          EndDate: select.value.time[1],
          lang: "zh"
        },
        responseType: "blob", // 必须
        timeout: 5000
      }
    );

    // 处理文件名
    // let fileName = "export.csv";
    const selectedOption = options.value.find(
      option => option.value === select.value.store
    );

    let fileName = `[${selectedOption.label}]每日营业报表_${select.value.time[0]}-${select.value.time[1]}.csv`;
    const disposition = response.headers["content-disposition"];
    if (disposition) {
      const match = disposition.match(/filename="?(.+)"?/);
      // if (match) fileName = match[1];
    }
    // 触发下载
    const blob = new Blob([response.data]);
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    isExporting.value = false;
  } catch (error) {
    isExporting.value = false;
    console.error("导出失败:", error);
    ElMessage.error(`导出失败: ${error.message}`);
  }
};
</script>

<template>
  <div>
    <el-select
      v-model="select.store"
      placeholder="请选择门店"
      class="search-input"
      clearable
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-date-picker
      v-model="select.time"
      type="daterange"
      start-placeholder="开始时间日期"
      end-placeholder="结束时间日期"
      format="YYYY-MM-DD"
      value-format="YYYYMMDD"
      style="margin-right: 10px"
    />
    <el-button type="primary" @click="search">
      <el-icon>
        <Search />
      </el-icon>
      查询
    </el-button>
    <el-button type="success" @click="derived">
      <el-icon><Upload /></el-icon>
      导出
    </el-button>

    <!-- 表格数据 -->
    <el-table
      :data="timeSlotsData?.baseData || []"
      v-loading="tableLoad"
      style="width: 98%; margin: 0px auto; height: 70vh"
      size="small"
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <!-- 基本信息列 -->
      <el-table-column
        :label="tableData[0]?.ShopName || '门店名称'"
        width="150"
        align="center"
      >
        <el-table-column
          prop="ReportDate"
          label="日期"
          width="100"
          align="center"
          :formatter="formatDate"
        />
        <el-table-column
          prop="Weekday"
          label="星期"
          width="100"
          align="center"
        />
      </el-table-column>

      <!-- 营收数据列 -->
      <el-table-column label="每日营收数据" align="center">
        <el-table-column
          prop="Revenue_Total"
          label="总营业收入"
          width="100"
          align="center"
        />
        <el-table-column
          prop="Revenue_DayTime"
          label="白天档"
          width="100"
          align="center"
        />
        <el-table-column
          prop="Revenue_NightTime"
          label="晚档"
          width="100"
          align="center"
        />
      </el-table-column>

      <!-- 每日带客数据 -->
      <el-table-column label="每日带客数据" width="120" align="center">
        <el-table-column
          prop="TotalBatchCount_AllDay"
          label="全天总营客批数"
          width="70"
          align="center"
        ></el-table-column>
        <el-table-column label="白天档" width="100" align="center">
          <el-table-column
            prop="DayTime_KPlusMeal_BatchCount"
            label="K+餐"
            width="100"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="DayTime_DirectFallBatch"
            label="直落"
            width="100"
            align="center"
          ></el-table-column>
        </el-table-column>
        <el-table-column label="晚上档" width="100" align="center">
          <el-table-column
            prop="NightTime_TotalBatch"
            label="20点后进场"
            width="100"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="NightTime_DirectFallBatch"
            label="直落"
            width="100"
            align="center"
          ></el-table-column>
        </el-table-column>
      </el-table-column>

      <!-- 用餐人数 -->
      <el-table-column label="用餐人数" align="center">
        <el-table-column
          prop="BuffetGuestCount"
          label="K+餐人数"
          width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="TotalDirectFallGuests"
          label="直落人数"
          width="100"
          align="center"
        ></el-table-column>
      </el-table-column>

      <!-- 动态时间段列 -->
      <el-table-column label="K+自助餐" align="center">
        <template
          v-for="slot in timeSlotsData?.timeSlots || []"
          :key="slot.timeSlot"
        >
          <el-table-column :label="slot.timeSlot" align="center" width="auto">
            <el-table-column prop="KPlus" label="K+" width="80" align="center">
              <template #default="{ row, $index }">
                {{
                  timeSlotsData.timeSlots.find(
                    s => s.timeSlot === slot.timeSlot
                  )?.data[$index]?.KPlus || 0
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="SpecialReservation"
              label="特权预约"
              width="80"
              align="center"
            >
              <template #default="{ row, $index }">
                {{
                  timeSlotsData.timeSlots.find(
                    s => s.timeSlot === slot.timeSlot
                  )?.data[$index]?.SpecialReservation || 0
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="Meituan"
              label="美团"
              width="80"
              align="center"
            >
              <template #default="{ row, $index }">
                {{
                  timeSlotsData.timeSlots.find(
                    s => s.timeSlot === slot.timeSlot
                  )?.data[$index]?.Meituan || 0
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="Douyin"
              label="抖音"
              width="80"
              align="center"
            >
              <template #default="{ row, $index }">
                {{
                  timeSlotsData.timeSlots.find(
                    s => s.timeSlot === slot.timeSlot
                  )?.data[$index]?.Douyin || 0
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="RoomFee"
              label="房费"
              width="80"
              align="center"
            >
              <template #default="{ row, $index }">
                {{
                  timeSlotsData.timeSlots.find(
                    s => s.timeSlot === slot.timeSlot
                  )?.data[$index]?.RoomFee || 0
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="PrevSlotDirectFall"
              label="上一档直落"
              width="105"
              align="center"
            >
              <template #default="{ row, $index }">
                {{
                  timeSlotsData.timeSlots.find(
                    s => s.timeSlot === slot.timeSlot
                  )?.data[$index]?.PrevSlotDirectFall || 0
                }}
              </template>
            </el-table-column>

            <el-table-column
              prop="Subtotal"
              label="小计"
              width="80"
              align="center"
            >
              <template #default="{ row, $index }">
                {{
                  timeSlotsData.timeSlots.find(
                    s => s.timeSlot === slot.timeSlot
                  )?.data[$index]?.Subtotal || 0
                }}
              </template>
            </el-table-column>
          </el-table-column>
        </template>
        <el-table-column
          prop="DayTime_KPlusMeal_BatchCount"
          label="K+餐批数"
          width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="DayTime_KPlusMeal_DirectFallBatch"
          label="K+餐直落批数"
          width="100"
          align="center"
        ></el-table-column>
      </el-table-column>
      <el-table-column
        prop="NightTime_EarlySlots_DirectFallBatch"
        label="17点档
          18点档
          19点档
          直落
          "
        width="65"
        align="center"
      ></el-table-column>

      <el-table-column label="K+自由餐" align="center">
        <el-table-column label="20:00-END" align="center">
          <el-table-column
            label="K+"
            prop="Night_FreeMeal_KPlus"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="特权预约"
            prop="Night_FreeMeal_SpecialReservation"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="美团"
            prop="Night_FreeMeal_Meituan"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="抖音"
            prop="Night_FreeMeal_Douyin"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="小计"
            prop="Night_FreeMeal_SubtotalBatch"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="K+自由餐消费金额"
            prop="Night_FreeMeal_Revenue"
            align="center"
          >
          </el-table-column>
        </el-table-column>
      </el-table-column>
      <el-table-column label="20点后进场" align="center">
        <el-table-column label="正价套餐" align="center">
          <el-table-column
            label="买断套餐"
            width="65"
            align="center"
            prop="After8PM_BuyoutBatch"
          ></el-table-column>
          <el-table-column
            label="畅饮套餐"
            width="65"
            align="center"
            prop="After8PM_AllYouCanDrinkPackageBatch"
          ></el-table-column>
          <el-table-column
            label="自由消套餐"
            align="center"
            prop="After8PM_FreeConsumptionPackageBatch"
          ></el-table-column>
        </el-table-column>

        <el-table-column label="促销(特惠套餐)" align="center">
          <el-table-column
            label="特权"
            align="center"
            prop="After8PM_Promo_SpecialReservation"
          ></el-table-column>
          <el-table-column
            label="美团"
            align="center"
            prop="After8PM_Promo_Meituan"
          ></el-table-column>

          <el-table-column
            label="抖音"
            align="center"
            prop="After8PM_Promo_Douyin"
          ></el-table-column>
        </el-table-column>

        <el-table-column
          label="房费"
          prop="After8PM_RoomFeeBatch"
          align="center"
        >
        </el-table-column>
        <el-table-column label="其他" prop="After8PM_OtherBatch" align="center">
        </el-table-column>
        <el-table-column
          label="批数小计"
          prop="After8PM_SubtotalBatch"
          align="center"
        >
        </el-table-column>
        <el-table-column
          label="20点后消费金额"
          prop="After8PM_Revenue"
          align="center"
        >
        </el-table-column>
      </el-table-column>

      <el-table-column label="招待" align="center">
        <el-table-column
          label="批数"
          prop="Complimentary_BatchCount"
          align="center"
        >
        </el-table-column>
        <el-table-column
          label="打折金额 免单金额"
          prop="Complimentary_Revenue"
          align="center"
        >
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped>
.search-input {
  width: 300px;
  margin: 10px 10px;
}
</style>
