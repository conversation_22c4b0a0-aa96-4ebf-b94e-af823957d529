<script setup lang="ts">
defineOptions({
  name: "Welcome"
});
</script>

<template>
  <div class="welcome-container p-6">
    <div class="text-center">
      <h1 class="text-4xl font-bold text-gray-800 mb-4">欢迎使用管理平台</h1>
      <p class="text-lg text-gray-600 mb-8">后台管理系统</p>

      <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">系统功能</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div class="p-4 bg-blue-50 rounded-lg">
              <h3 class="font-semibold text-blue-800 mb-2">用户管理</h3>
              <p class="text-sm text-blue-600">管理系统用户和权限</p>
            </div>
            <div class="p-4 bg-green-50 rounded-lg">
              <h3 class="font-semibold text-green-800 mb-2">数据统计</h3>
              <p class="text-sm text-green-600">查看业务数据和报表</p>
            </div>
            <div class="p-4 bg-purple-50 rounded-lg">
              <h3 class="font-semibold text-purple-800 mb-2">系统配置</h3>
              <p class="text-sm text-purple-600">配置系统参数和设置</p>
            </div>
            <div class="p-4 bg-orange-50 rounded-lg">
              <h3 class="font-semibold text-orange-800 mb-2">日志管理</h3>
              <p class="text-sm text-orange-600">查看系统操作日志</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.welcome-container {
  min-height: calc(100vh - 200px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.bg-white {
  transition: all 0.3s ease;
}

.bg-white:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
</style>
