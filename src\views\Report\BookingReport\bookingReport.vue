<script setup>
import { ref } from "vue";
import { Search, Upload } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import axios from "axios";
import { onMounted, computed } from "vue";
const stores = ref([]);
const selectedStore = ref(null); // 用于存储选中的门店ID
const tableLoad = ref(false);
// 获取前一天日期
const getTodayRange = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate() - 1).padStart(2, "0");
  const todayStr = `${year}${month}${day}`;
  return [todayStr, todayStr]; // [YYYYMMDD, YYYYMMDD]
};
const select = ref({
  time: getTodayRange()
});

// 计算时间段数据
const timeSlotsData = computed(() => {
  if (!tableData.value.length) return [];
  console.log("格式化");
  const allSlots = {};
  const allBaseData = [];

  // 处理所有数据
  tableData.value.forEach(item => {
    const slots = {};
    const baseData = {};

    // 分离时间段数据和其他数据
    Object.keys(item).forEach(key => {
      // 正则匹配时间段字段，例如 "10:00-11:00_fieldname"
      //const timeMatch = key.match(/^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$/);
      const timeMatch = key.match(/^(\d{2}:\d{2})(?:-(\d{2}:\d{2}))?_(.+)$/);
      if (timeMatch) {
        //如果匹配成功 解构时间字段和原始字段名
        const [_, startTime, endTime, field] = timeMatch;
        const timeSlot =
          endTime !== undefined
            ? `${startTime}-${endTime}`
            : `${startTime}-END`;
        // 如果slots对象中还没有这个时间段，初始化它
        if (!slots[timeSlot]) {
          slots[timeSlot] = {};
        }
        // 将时间段数据保存
        slots[timeSlot][field] = item[key];
      } else {
        // 将非时间段数据保存到baseData中
        baseData[key] = item[key];
      }
    });

    // 将当前条目的数据保存
    allBaseData.push(baseData);

    // 合并时间段数据
    Object.keys(slots).forEach(timeSlot => {
      // 如果 allSlots 中还没有这个时间段，初始化它为数组
      if (!allSlots[timeSlot]) {
        allSlots[timeSlot] = [];
      }
      // 将当前时间段的数据存入 allSlots 对应时间段的数组中
      allSlots[timeSlot].push(slots[timeSlot]);
    });
  });
  return {
    baseData: allBaseData, // 现在包含所有条目的基础数据
    timeSlots: Object.keys(allSlots).map(timeSlot => ({
      timeSlot,
      data: allSlots[timeSlot] // 每个时间段包含所有条目的数据
    }))
  };
});

onMounted(async () => {
  try {
    const response = await axios.get("http://183.63.130.69:200/ExecUse/Index", {
      params: {
        Ex: "GrouponBase.dbo.Ex_SelTable",
        TableName: "Mims.dbo.ShopInfo"
      }
    });
    let responseData = response.data;
    if (typeof responseData === "string" && responseData.startsWith("(")) {
      responseData = JSON.parse(responseData.replace(/^\((.*)\)$/, "$1"));
    }
    stores.value = responseData.ObjceData.filter(
      shop =>
        shop.IsUse && shop.ShopName !== "清远店" && shop.ShopName !== "黄岐店"
    ) // 只保留 IsUse 为 true 的店铺
      .map(shop => ({
        value: shop.ShopID, // 使用 ShopID 作为 value
        label: shop.ShopName // 使用 ShopName 作为 label
      }));
  } catch (err) {
    console.error("初始化失败:", err);
  }
});

const selectStore = storeId => {
  selectedStore.value = storeId;
};

// 搜索
const search = async () => {
  // console.log("搜索门店:", selectedStore.value);
  // console.log("搜索时间范围:", select.value.time);
  if (select.value.time.length !== 2 || selectedStore.value == null) {
    ElMessage.error("请选择完整的时间范围和门店！");
    return;
  }
  await getData();
};

const getData = async () => {
  tableLoad.value = true;
  const response = await axios.get("http://183.63.130.69:88/ExecUse/index", {
    params: {
      Ex: "OperateData.dbo.usp_GetBookingReport",
      ShopId: selectedStore.value,
      BeginDate: select.value.time[0],
      EndDate: select.value.time[1],
      lang: "en"
    }
  });
  let responseData = response.data;
  if (typeof responseData === "string" && responseData.startsWith("(")) {
    responseData = JSON.parse(responseData.replace(/^\((.*)\)$/, "$1"));
  }
  console.log(responseData);
  tableData.value = responseData.ObjceData || []; // 确保是数组类型，避免空值问题
  tableLoad.value = false;
  console.log(timeSlotsData.value);
  if (tableData.value.length === 0) {
    ElMessage("当前选择的日期没有查询到数据！");
  }
};

const isExporting = ref(false);
// 导出报表
const derived = async () => {
  if (selectedStore.value == "all" || select.value.time.length !== 2) {
    ElMessage({
      showClose: true,
      message: "请选择门店和完整的时间范围！",
      type: "error"
    });
    return;
  }
  try {
    // 验证参数
    isExporting.value = true;
    const response = await axios.get(
      "http://183.63.130.69:88/ExportToCsv/export",
      {
        params: {
          Ex: "OperateData.dbo.usp_GetBookingReport",
          ShopId: selectedStore.value,
          BeginDate: select.value.time[0],
          EndDate: select.value.time[1],
          lang: "zh"
        },
        responseType: "blob", // 必须
        timeout: 10000
      }
    );
    // 处理文件名
    // let fileName = "export.csv";
    const selectedOption = stores.value.find(
      option => option.value === selectedStore.value
    );

    let fileName = `[${selectedOption.label}]每日预定报表_${select.value.time[0]}-${select.value.time[1]}.csv`;
    const disposition = response.headers["content-disposition"];
    if (disposition) {
      const match = disposition.match(/filename="?(.+)"?/);
      // if (match) fileName = match[1];
    }
    // 触发下载
    const blob = new Blob([response.data]);
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    isExporting.value = false;
  } catch (error) {
    isExporting.value = false;
    console.error("导出失败:", error);
    ElMessage.error(`导出失败: ${error.message}`);
  }
};

// 表格数据格式化时间
const formatDate = (row, column, cellValue) => {
  if (column.property === "ReportDate") {
    // 处理 /Date(1753286400000)/ 格式
    const timestamp = parseInt(cellValue.match(/\d+/)[0]);
    return new Date(timestamp).toLocaleDateString();
  }
  return cellValue;
};
const tableData = ref([]);
</script>

<template>
  <div>
    <div class="ml-2 mt-2" style="padding-top: 10px">
      <el-select
        v-model="selectedStore"
        placeholder="请选择门店"
        style="width: 300px; margin-right: 10px"
        clearable
      >
        <el-option
          v-for="item in stores"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-date-picker
        v-model="select.time"
        type="daterange"
        start-placeholder="开始时间日期"
        end-placeholder="结束时间日期"
        format="YYYY-MM-DD"
        value-format="YYYYMMDD"
      />
      <el-button class="ml-2" type="primary" @click="search">
        <el-icon>
          <Search />
        </el-icon>
        查询
      </el-button>
      <el-button type="success" @click="derived">
        <el-icon><Upload /></el-icon>
        导出
      </el-button>
    </div>

    <!-- 表格数据 -->
    <el-table
      :data="timeSlotsData?.baseData || []"
      border
      style="width: 99%; margin: 10px auto; height: 72vh"
      v-loading="tableLoad"
      :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column width="100" prop="ShopName" label="门店" />
      <el-table-column
        prop="ReportDate"
        label="日期"
        :formatter="formatDate"
        width="100"
      />
      <el-table-column prop="Weekday" label="星期" width="80" />
      <el-table-column prop="TotalRevenue" label="当天总营业额" width="110" />
      <!-- 动态时间段列 -->
      <el-table-column
        v-if="!timeSlotsData?.timeSlots"
        label="营业时间段"
        align="center"
      ></el-table-column>
      <template
        v-for="slot in timeSlotsData?.timeSlots || []"
        :key="slot.timeSlot"
      >
        <el-table-column :label="slot.timeSlot" align="center" width="auto">
          <el-table-column
            prop="BookedRooms"
            label="预定房间数"
            width="100"
            align="center"
          >
            <template #default="{ row, $index }">
              {{
                timeSlotsData.timeSlots.find(s => s.timeSlot === slot.timeSlot)
                  ?.data[$index]?.BookedRooms || 0
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="BookedGuests"
            label="预定人数"
            width="100"
            align="center"
          >
            <template #default="{ row, $index }">
              {{
                timeSlotsData.timeSlots.find(s => s.timeSlot === slot.timeSlot)
                  ?.data[$index]?.BookedGuests || 0
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="OccupiedRooms"
            label="待客房间数"
            width="95"
            align="center"
          >
            <template #default="{ row, $index }">
              {{
                timeSlotsData.timeSlots.find(s => s.timeSlot === slot.timeSlot)
                  ?.data[$index]?.OccupiedRooms || 0
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="OccupiedGuests"
            label="待客人数"
            width="85"
            align="center"
          >
            <template #default="{ row, $index }">
              {{
                timeSlotsData.timeSlots.find(s => s.timeSlot === slot.timeSlot)
                  ?.data[$index]?.OccupiedGuests || 0
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="OccupancyRate"
            label="开房率"
            width="100"
            align="center"
          >
            <template #default="{ row, $index }">
              {{
                timeSlotsData.timeSlots.find(s => s.timeSlot === slot.timeSlot)
                  ?.data[$index]?.OccupancyRate || 0
              }}
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<style lang="scss" scoped>
.header {
  font: 100 15px "微软雅黑";
  display: flex;
  align-items: center;
  padding-top: 5px;
}
.store-list {
  display: flex;
  flex-direction: row;
  margin-left: 5px;
  margin-bottom: 5px;
}
.store-item {
  margin-right: 10px;
  padding: 5px;
  background-color: #249672;
  color: white;
  cursor: pointer;
  margin-top: 5px;
}
.store-item:hover {
  background-color: #ff7f50;
}

.store-item.active {
  background-color: #ff7f50;
}
</style>
