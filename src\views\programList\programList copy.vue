<template>
  <div>
    <div style="display: flex; justify-content: flex-end; align-items: center">
      <el-input
        style="margin-right: 10px; width: 200px"
        v-model="bodyMsg"
        placeholder="请搜索节目名称"
      ></el-input>
      <el-button>搜索</el-button>
      <el-button @click="newProgram">新建投放</el-button>
    </div>
    <el-card style="max-width: 480px">
      <p v-for="o in 4" :key="o" class="text item">页面显示</p>
      <template #footer>
        <div
          style="display: flex; justify-content: flex-end; align-items: center"
        >
          <el-button>删除节目</el-button>
          <el-button>结束投放</el-button>
          <el-button>播放设备</el-button>
          <el-button type="primary">编辑节目</el-button>
        </div>
      </template>
    </el-card>
    <!-- 新建投放任务弹窗 -->
    <el-dialog
      v-model="dialogFormVisible"
      title="新建投放任务"
      width="100%"
      style="height: 100%; margin: 0px"
      overflow
    >
      <div style="width: 100%; height: 100%">
        <div
          style="display: flex; justify-content: flex-end; align-items: center"
        >
          <el-button type="primary" @click="launch">投放</el-button>
        </div>
        <el-tabs
          tab-position="left"
          style="height: 100%"
          class="demo-tabs"
          @update:modelValue="changeChoose"
        >
          <el-tab-pane label="选择画布">
            <el-tabs
              v-model="chooseCanvas"
              tab-position="top"
              style="height: 100%"
              class="demo-tabs"
            >
              <el-tab-pane name="1" label="横向" class="p-0 m-0">
                <div class="aspect-ratio-16-9">
                  <div v-if="list.length > 0" style="width: 100%; height: 100%">
                    <img
                      :src="`https://hdcore.tang-hui.com.cn/${list[0].FilePath}`"
                      alt="图片"
                      style="width: 100%; height: 100%; object-fit: content"
                    />
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="2" label="纵向">
                <div class="aspect-ratio-9-16">
                  <div v-if="list.length > 0" style="width: 100%; height: 100%">
                    <img
                      :src="list[0].url"
                      alt="图片"
                      style="width: 100%; height: 100%; object-fit: content"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <el-tab-pane label="选择布局">
            <el-tabs
              class="demo-tabs"
              v-model="chooseLayout"
              @update:modelValue="handleChooseLayout"
            >
              <!-- 默认布局 -->
              <el-tab-pane label="默认布局" name="defaultLayout">
                <div class="aspect-ratio-9-16" v-if="chooseCanvas === '2'">
                  <div v-if="list.length > 0" style="width: 100%; height: 100%">
                    <img
                      :src="list[0].url"
                      alt="图片"
                      style="width: 100%; height: 100%; object-fit: content"
                    />
                  </div>
                </div>
                <div class="aspect-ratio-16-9" v-if="chooseCanvas === '1'">
                  <div v-if="list.length > 0" style="width: 100%; height: 100%">
                    <img
                      :src="list[0].url"
                      alt="图片"
                      style="width: 100%; height: 100%; object-fit: content"
                    />
                  </div>
                </div>
              </el-tab-pane>
              <!-- 二分布局 -->
              <el-tab-pane label="二分布局" name="twoLayout">
                <div class="aspect-ratio-9-16" v-if="chooseCanvas === '2'">
                  <div
                    id="grid-stack-2"
                    ref="gridStackRef"
                    class="grid-stack"
                    style="
                      border: 2px dashed lightgrey;
                      width: 100%;
                      height: 100%;
                    "
                  >
                </div>
                </div>
                <div class="aspect-ratio-16-9" v-if="chooseCanvas === '1'">
                  <div
                    id="grid-stack-1"
                    class="grid-stack"
                    style="width: 100%; height: 100%"
                  >
                 <!-- 用于显示图片的元素，初始时隐藏 -->
                </div>
                </div>
              </el-tab-pane>
              <!-- 三分布局 -->
              <el-tab-pane label="三分布局" name="threeLayout">
                <div class="aspect-ratio-9-16" v-if="chooseCanvas === '2'">
                  <div
                    id="grid-stack-3"
                    ref="gridStackRef"
                    class="grid-stack"
                    style="
                      border: 2px dashed lightgrey;
                      width: 100%;
                      height: 100%;
                    "
                  ></div>
                </div>
                <div class="aspect-ratio-16-9" v-if="chooseCanvas === '1'">
                  <div
                    id="grid-stack-4"
                    ref="gridStackRef"
                    class="grid-stack"
                    style="
                      border: 2px dashed lightgrey;
                      width: 100%;
                      height: 100%;
                    "
                  ></div>
                </div>
              </el-tab-pane>
              <!-- 自定义布局 -->
              <el-tab-pane label="自定义布局" name="autoLayout">
  <el-container>
    <el-aside width="250px">
      <div id="trash" class="delete-grid">
        放在这里删除小部件！
      </div>
      <div class="add-grid" draggable="true">
        拖入添加小部件！
      </div>
    </el-aside>
    <el-main>
      <div class="aspect-ratio-9-16" v-if="chooseCanvas === '2'">
        <div
          id="grid-stack-auto-column"
          class="grid-stack"
          style="
            border: 2px dashed lightgrey;
            width: 100%;
            height: 100%;
          "
        ></div>
      </div>
      <div class="aspect-ratio-16-9" v-if="chooseCanvas === '1'">
        <div
          id="grid-stack-auto-row"
          class="grid-stack"
          style="
            border: 2px dashed lightgrey;
            width: 100%;
            height: 100%;
          "
        ></div>
      </div>
    </el-main>
  </el-container>
</el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <!-- 文件上传盒子 -->
          <el-button type="primary" @click="chooseMaterial">选择素材</el-button>
          <div
            style="
              width: 100%;
              max-height: 260px;
              border: 1px solid gray;
              margin-top: 15px;
              padding: 10px;
            "
          >
            <el-scrollbar height="250px">
              <p v-if="list.length == 0">您已选择的素材会展示在这里</p>
              <el-row>
                <el-col v-for="item in list" :span="2">
                  <img
                    v-if="item.FormatType.includes('image')"
                    :src="`https://hdcore.tang-hui.com.cn/${item.FilePath}`"
                    style="height: 230px"
                  />
                  <video
                    v-if="item.FormatType.includes('mp4')"
                    controls
                    :src="`https://hdcore.tang-hui.com.cn/${item.FilePath}`"
                    style="height: 230px"
                  ></video>
                  <audio
                    v-if="item.FormatType.includes('mp3')"
                    controls
                    :src="`https://hdcore.tang-hui.com.cn/${item.FilePath}`"
                    style="height: 230px"
                  ></audio>
                </el-col>
              </el-row>
            </el-scrollbar>
          </div>
        </el-tabs>
        <!-- 自定义布局弹窗 -->
        <!-- <el-dialog
          v-model="dialogVisible"
          title="自定义布局"
          fullscreen
          width="100%"
          draggable
        >
          <div id="home">
            <div class="left-box">
              <div id="trash" class="delete-grid">放在这里删除小部件！</div>
              <div class="add-grid" draggable="true">拖入添加小部件！</div>
            </div>
            <div class="right-box">
              <h2 style="text-align: center">操作布局面板</h2>
              <el-button
                type="primary"
                @click="saveLayout()"
                style="width: 100px; height: 35px; border-radius: 10px"
                >保存</el-button
              >
              <div
                class="grid-stack"
                id="advanced-grid"
                style="border: 2px dashed lightgrey"
              ></div>
            </div>
          </div>
        </el-dialog> -->
      </div>
    </el-dialog>
    <!-- 投放按钮弹窗 -->
    <el-dialog v-model="launchDialog" title="投放信息" width="600" draggable>
      <!-- 添加弹性容器包裹卡片 -->
      <el-form
        :model="lanchFrom"
        label-width="auto"
        style="max-width: 500px"
        :rules="rules"
      >
        <el-form-item label="投放名称" prop="name">
          <el-input v-model="lanchFrom.name" autocomplete="off" />
        </el-form-item>
        <el-form-item
          label="投放时间"
          placeholder="请选择投放时间"
          prop="lanchRespect"
        >
          <el-radio-group v-model="lanchFrom.lanchtime">
            <!-- <el-radio value="All">长期投放</el-radio> -->
            <el-radio value="All">定时投放</el-radio>
          </el-radio-group>
            <el-form-item>
          <el-col :span="11">
            <el-date-picker
              v-model="lanchFrom.putBegin"
              type="datetime"
              placeholder="选择开始投放时间"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">-</span>
          </el-col>
          <el-col :span="11">
            <el-date-picker
            type="datetime"
              v-model="lanchFrom.putEnd"
              placeholder="选择结束投放时间"
              style="width: 100%"
            />
          </el-col>
        </el-form-item>
          <!-- <el-select v-model="lanchFrom.lanchRespect">
            <el-option label="每天重复" value="shanghai" />
            <el-option label="每周重复" value="beijing" />
            <el-option label="每年重复" value="beijing" />
          </el-select> -->
        </el-form-item>
        <!-- <el-form-item label="全天播放">
          <el-switch v-model="lanchFrom.allDay" />
        </el-form-item> -->
        <!-- <el-form-item label="播放时间" v-if="lanchFrom.allDay === false">
          <el-col :span="11">
            <el-time-picker
              v-model="lanchFrom.beginTime"
              type="date"
              placeholder="选择开始播放时间"
              style="width: 100%"
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">-</span>
          </el-col>
          <el-col :span="11">
            <el-time-picker
              v-model="lanchFrom.endTime"
              placeholder="选择结束播放时间"
              style="width: 100%"
            />
          </el-col>
        </el-form-item> -->
        <el-form-item label="投放设备">
          <el-button @click="lanchEquipment">选择投放设备</el-button>
        </el-form-item>
         <el-form-item label="已选择设备"  v-if="selectEquiment.length >0">
          <span v-if="selectEquiment.length >0" v-for="item in selectEquiment" style="margin:0 10px; color: gray;">
            {{ item.DeviceName }}
          </span>
        </el-form-item>
      </el-form>

      <el-dialog
        v-model="equipmentDialog"
        width="1000"
        draggable
        title="选择播放设备"
        append-to-body
        aligin-center="true"
        style="max-height: 600px"
      >
        <el-scrollbar height="400px">
          <!-- <div class="material-container"> -->
          <el-row :gutter="4">
            <el-col
              :span="12"
              v-for="item in equipmentList"
              style="margin-bottom: 10px"
            >
              <el-row>
                <el-col :span="2">
                  <el-checkbox
                    v-model="item.selected"
                    :true-label="item.DeviceID"
                    @change="handleSelectEquipment(item)"
                    style="margin-left: 10px"
                  ></el-checkbox>
                </el-col>
                <el-col :span="22">
                  <el-card
                    :key="item.DeviceID"
                    style="
                      max-width: 350px;
                      height: 150px;
                      display: flex;
                      align-items: center;
                      gap: 20px;
                    "
                  >
                    <img
                      src="@/assets/equiment.png"
                      style="height: 100px; flex-shrink: 0; margin-right: 30px"
                    />
                    <div style="text-align: left; float: right">
                      <p>
                        名称：<span class="equMsg">{{ item.DeviceName }}</span>
                      </p>
                      <p>
                        型号：<span class="equMsg">{{ item.DeviceModel }}</span>
                      </p>
                      <p>
                        尺寸：<span class="equMsg">{{
                          item.DeviceResolution
                        }}</span>
                      </p>
                      <p>
                        形状：<span class="equMsg">{{
                          item.DeviceOrientation
                        }}</span>
                      </p>
                      <P
                        >状态：<span class="equMsg"
                          ><el-tag
                            v-if="item.OnlineStatus.StatusID === 2"
                            :key="item.DeviceID"
                            type= "info"
                            effect="light"
                            round
                          >
                            {{ item.OnlineStatus.StatusName }}
                          </el-tag>
                          <el-tag
                            v-if="item.OnlineStatus.StatusID === 1"
                            :key="item.DeviceID"
                            type= "success"
                            effect="light"
                            round
                          >
                            {{ item.OnlineStatus.StatusName }}
                          </el-tag>
                          </span
                        ></P
                      >
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <!-- </div> -->
        </el-scrollbar>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="equipmentDialog = false">取消</el-button>
            <el-button type="primary" @click="sureSelectEquipment"> 确定选择 </el-button>
          </div>
        </template>
      </el-dialog>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="launchDialog = false">取消</el-button>
          <el-button type="primary" @click="addPutMark"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 素材选择弹窗 -->
    <el-dialog v-model="programDialog" title="素材选择" width="1000" draggable>
      <!-- 添加弹性容器包裹卡片 -->
      <div class="material-container">
        <el-card
          v-for="item in materialList"
          :key="item.id"
          class="material-card"
          style="max-width: 300px"
        >
          <!-- 添加多选框 -->
          <div class="card-header">
            <el-checkbox
              v-model="item.selected"
              :true-label="item.id"
              @change="handleSelectionChange(item)"
            ></el-checkbox>
          </div>

          <!-- 图片区域 -->
          <div class="card-image">
            <img
              v-if="item.FormatType.includes('image')"
              :src="`https://hdcore.tang-hui.com.cn/${item.FilePath}`"
              style="width: 100%"
            />
            <video
              v-if="item.FormatType.includes('mp4')"
              controls
              :src="`https://hdcore.tang-hui.com.cn/${item.FilePath}`"
              style="width: 100%"
            ></video>
            <audio
              v-if="item.FormatType.includes('mp3')"
              controls
              :src="`https://hdcore.tang-hui.com.cn/${item.FilePath}`"
              style="width: 100%"
            ></audio>
          </div>
        </el-card>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="programDialog = false">取消</el-button>
          <el-button type="primary" @click="getchooseList"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>

// var 布局结构 = [{
//   id: "1",
//   name: "二分屏",
//   具体布局内容: [
//     {x,y,w,h,content: "素材1" }, {x,y,w,h,content: "素材2" 
//   ]
// }, {
//   id: "2",
//   name: "三分屏",
//  具体布局内容: [
//     {x,y,w,h,content: "素材1" }, {x,y,w,h,content: "素材2" 
//   ]
//  ]
// var 容器选择素素材 = [];
// var 已选的节目素材 = [];
// vaer 当前选择的下标 = 1;

// 已选的节目素材[当前选择的下标] = [1.jpg, 2.jpg]
// 已选的节目素材 = 已选的节目素材[当前选择的下标]

import { Delete, Download, Plus, ZoomIn } from "@element-plus/icons-vue";
import { getMaterial, getEquipment,addPut } from "@/api/programList";
import { onMounted, watch, ref, nextTick, reactive } from "vue";
import { GridStack } from "gridstack";
import "gridstack/dist/gridstack.min.css";
const bodyMsg = ref("");
const grid = ref(null); // 统一使用这个ref管理实例
const gridStackRef = ref(null);
const gridData = [];
const addWidgetContent = ref(0);
const dialogFormVisible = ref(false);
const dialogVisible = ref(false);
const chooseCanvas = ref("1"); //选择画布
const dialogImg = ref(false); //图片缩略图弹窗
const dialogImageUrl = ref(""); //图片缩略图弹窗地址
const list = ref([]);
const chooseLayout = ref("defaultLayout"); //选择布局
const formLabelWidth = "140px";
const imgUrl = ref("");
const programDialog = ref(false); //素材弹窗
const materialList = ref([]);
const launchDialog = ref(false); //点击投放按钮后的弹窗

const layoutDemo = [
  {
    id: "1", name: "二分屏", layout: 
      [
        {
        name: "横屏",
        layoutList: [
        { x: 0, y: 0, w: 6, h: 12, content: "素材1" },
        { x: 6, y: 0, w: 6, h: 12, content: "素材2" }
        ]
        },
        {
        name: "竖屏",
        layoutList: [
        { x: 0, y: 0, w: 12, h: 3, content: "上面内容" },
        { x: 0, y: 6, w: 12, h: 3, content: "下面内容" }
        ]
        }
      ]
  },
  {
    id: "2", name: "三分屏", layout: [
      {
        name: "横屏",
        layoutList: [
          { x: 0, y: 0, w: 4, h: 12, content: "素材1" },
          { x: 4, y: 0, w: 4, h: 12, content: "素材2" },
          { x: 8, y: 0, w: 4, h: 12, content: "素材3" }
        ]
      },
      {
        name: "竖屏",
        layoutList: [
          { x: 0, y: 0, w: 12, h: 4, content: "上面内容" },
          { x: 0, y: 4, w: 12, h: 4, content: "中间内容" },
          { x: 0, y: 8, w: 12, h: 4, content: "下面内容" }
        ]
      }
  ]},
  { id: "3", name: "自适应布局" },
]

const newProgram = () => {
  dialogFormVisible.value = true;
};
const handleTabChange = () => {
  console.log("当前选中的画布:", chooseCanvas.value);
};

const handleRemove = file => {
  list.value = list.value.filter(item => item.uid !== file.uid);
  console.log("移除后的文件列表:", list.value);
};

const handlePictureCardPreview = file => {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
};

const num = ref(1);
const handleChange = value => {
  console.log(value);
};
const changeChoose = newValue => {
  console.log(newValue);
  if (newValue) {
  }
};
// 根据画布渲染gridstack布局
const handleChooseLayout = async newValue => {
  await nextTick(); // 等待DOM更新完成
  console.log("当前选中的画布:", newValue);
  // 在这里调用你想要的方法
  if (newValue == "twoLayout") {
    GridStackInit(); // 调用初始化函数
  } else if (newValue == "threeLayout") {
    console.log("走这里");
    threeGridStackInit();
  } else if (newValue == "autoLayout") {
    autoGridStackInit();
  }
};


const selectedImages = ref([]);
const widgetSelections = ref([]);
const activeColor = ref("black"); // 激活的颜色

// const GridStackInit = () => {
//   const gridStackRef1 = document.querySelector("#grid-stack-1");
//   const gridStackRef2 = document.querySelector("#grid-stack-2");
//   // 确保DOM已经渲染
//   if (!gridStackRef1 && !gridStackRef2) {
//     console.error("GridStack容器未找到");
//     return;
//   }

//   const renderLayout = (gridStackRef, layout) => {
//     const containerHeight = gridStackRef.clientHeight;
//     const options = {
//       dragOut: true,
//       margin: 0,
//       allowHtml: true,
//       cellHeight: containerHeight / 6,
//       column: 12,
//       maxRow: 6,
//       disableDrag: true,
//       disableResize: true,
//       animate: false
//     };

//     GridStack.renderCB = (el, node) => {
//       el.innerHTML = node.content || "";
//     };
//     grid.value = GridStack.init(options, gridStackRef);
//     if (grid.value) {
//       if (layout === "twoRow") {
//         // 二分屏布局
//         //找到数据布局
//         //遍历布局内容,假设有两个
//         容器选择素素材=[];
//         for (let i = 0; i < 2; i++) { 

//           const widget = grid.value.addWidget({ x: 0, y: 0, w: 6, h: 6, content: '左边内容' });
//           widget.addEventListener("click", () => handleWidgetClick(i));
//           容器选择素素材.push([])
//         }
//         const widget1 = grid.value.addWidget({ x: 0, y: 0, w: 6, h: 6, content: '左边内容' });
//         const widget2 = grid.value.addWidget({ x: 6, y: 0, w: 6, h: 6, content: "右边内容" });
//          // 初始化选中状态数组
//         widgetSelections.value = [false, false];

//         // 为每个小部件添加点击事件监听器
//         widget1.addEventListener("click", () => handleWidgetClick(0));
//         widget2.addEventListener("click", () => handleWidgetClick(1));
//       } else if (layout === "twoColumn") {
//         // 二分竖屏
//         const widget1 = grid.value.addWidget({ x: 0, y: 0, w: 12, h: 3, content: "上面内容" });
//        const widget2 =  grid.value.addWidget({ x: 0, y: 6, w: 12, h: 3, content: "下面内容" });
//         // 初始化选中状态数组
//         widgetSelections.value = [false, false, false];

//         // 为每个小部件添加点击事件监听器
//         widget1.addEventListener("click", () => handleWidgetClick(0));
//         widget2.addEventListener("click", () => handleWidgetClick(1));
//       }
//     }
//   };

//   if (gridStackRef1) {
//     renderLayout(gridStackRef1, "twoRow");
//   }
//   if (gridStackRef2) {
//     renderLayout(gridStackRef2, "twoColumn");
//   }
// };

// 处理小部件选中
const handleWidgetClick = (index) => {
  programDialog.value = true;
//   const targetDiv = e.target; // 获取点击的元素

// // 确保目标是 .up 或其父元素
// const widgetContent = targetDiv.closest('.grid-stack-item-content');
// if (widgetContent) {
//   // 添加/移除类
//   widgetContent.classList.toggle('active');

//   // 直接修改样式（优先级更高）
//   widgetContent.style.borderColor = '#409eff';
//   widgetContent.querySelector('.up').style.color = 'red';
// }
//   widgetSelections.value = widgetSelections.value.map((_, i) => i === index);
// console.log("当前选中状态：", widgetSelections.value);
//   // 根据选中状态显示或隐藏图片
//   if (widgetSelections.value[index]) {
//     // 这里可以添加逻辑来选择要显示的图片，例如从文件上传或其他数据源获取
//     selectedImages.value = ["path/to/your/image1.jpg", "path/to/your/image2.jpg"];
//   } else {
//     selectedImages.value = [];
  //   }

};

const isWidgetSelected = (index) => {
  return widgetSelections.value[index];
};
// const threeGridStackInit = () => {
//   const gridStackRef3 = document.querySelector("#grid-stack-3");
//   const gridStackRef4 = document.querySelector("#grid-stack-4");
//   // 确保DOM已经渲染
//   if (!gridStackRef3 && !gridStackRef4) {
//     console.error("GridStack容器未找到");
//     return;
//   }

//   const renderLayout = (gridStackRef, layout) => {
//     const containerHeight = gridStackRef.clientHeight;
//     const options = {
//       dragOut: true,
//       margin: 0,
//       allowHtml: true,
//       cellHeight: containerHeight / 6,
//       column: 12,
//       maxRow: 6,
//       disableDrag: true,
//       disableResize: true,
//       animate: false
//     };

//     GridStack.renderCB = (el, node) => {
//       el.innerHTML = node.content || "";
//     };
//     grid.value = GridStack.init(options, gridStackRef);
//     if ( grid.value) {
//       if (layout === "threeRow") {
//          grid.value.addWidget({ x: 0, y: 0, w: 12, h: 2, content: "上面内容" });
//          grid.value.addWidget({ x: 0, y: 4, w: 12, h: 2, content: "中间内容" });
//          grid.value.addWidget({ x: 0, y: 8, w: 12, h: 2, content: "下面内容" });
//       } else if (layout === "threeColumn") {
//          grid.value.addWidget({ x: 0, y: 0, w: 4, h: 6, content: "上面内容" });
//          grid.value.addWidget({ x: 4, y: 0, w: 4, h: 6, content: "中间内容" });
//          grid.value.addWidget({ x: 8, y: 0, w: 4, h: 6, content: "下面内容" });
//       }
//     }
//   };
//   if (gridStackRef3) {
//     renderLayout(gridStackRef3, "threeRow");
//   }
//   if (gridStackRef4) {
//     renderLayout(gridStackRef4, "threeColumn");
//   }
// };
// /**
//  * 初始化自动网格布局
//  *
//  * 根据不同的布局类型（行或列）初始化网格布局，并为网格添加拖拽事件和拖入事件处理逻辑。
//  */
// const autoGridStackInit = () => {
//   const autoCridStack1 = document.querySelector("#grid-stack-auto-row");
//   const autoGridStack2 = document.querySelector("#grid-stack-auto-coloumn");
//   // 确保DOM已经渲染
//   if (!autoCridStack1 && !autoGridStack2) {
//     console.error("GridStack容器未找到");
//     return;
//   }

//   const renderLayout = (gridStackRef, layout) => {
//     const containerHeight = gridStackRef.clientHeight;
//     console.log(containerHeight);
//     const options = {
//       dragOut: true,
//       margin: 0,
//       allowHtml: true,
//       cellHeight: containerHeight / 6,
//       column: 12,
//       maxRow: 6,
//       maxHeight: containerHeight,
//       minHeight:6,
//       animate: false,
//       acceptWidgets: true, //接受从其他网格或外部拖动的小部件
//       dragIn: ".add-grid", //可以从外部拖动的类
//       dragInOptions: {
//         revert: "invalid",
//         scroll: false, //当元素被拖动到网格的底部或顶部时启用或禁用滚动
//         appendTo: "body", //添加到body中
//         helper: "clone" //放置时的辅助函数=>克隆
//       }, //可以从外部拖动类的配置
//       removable: "#trash", //在拖动到网格外时删除小部件的类
//       removeTimeout: 100 //在拖动到网格外时删除小部件之前的时间 100毫
//     };
//     GridStack.renderCB = (el, node) => {
//       el.innerHTML = node.content || "";
//     };
//      grid.value = GridStack.init(options, gridStackRef);

//     // 添加拖拽事件监听
//     const draggableItems = document.querySelectorAll(".add-grid");
//     draggableItems.forEach(item => {
//       item.addEventListener("dragstart", e => {
//         // 设置拖拽数据，可以包含小部件的配置信息
//         e.dataTransfer.setData(
//           "text/plain",
//           JSON.stringify({
//             content: `<div class="widget-content">自定义小部件</div>`,
//             w: 4, // 默认宽度
//             h: 2 // 默认高度
//           })
//         );
//       });
//     });

//     // 处理从外部拖入的事件
//      grid.value.on("added", (e, items) => {
//       console.log("添加了新小部件:", items);
//       // 这里可以添加自定义逻辑，例如设置小部件的样式或内容
//     });
//     if ( grid.value) {
//       if (layout === "autoRow") {
//          grid.value.addWidget({ x: 0, y: 0, w: 0, h: 0, content: "布局" });
//       } else if (layout === "autoColumn") {
//          grid.value.addWidget({ x: 0, y: 0, w: 0, h: 0, content: "布局" });
//       }
//     }
//   };
//   if (autoCridStack1) {
//     renderLayout(autoCridStack1, "autoRow");
//   }
//   if (autoGridStack2) {
//     renderLayout( autoGridStack2, "autoColumn");
//   }
// };
const selectdItem = ref([]);
const handleSelectionChange = item => {
  if (item.selected) {
    selectdItem.value.push(item);
  } else {
    selectdItem.value = selectdItem.value.filter(i => i.FileID !== item.FileID);
  }
};
const getchooseList = () => {
  console.log(selectdItem.value);
  list.value = selectdItem.value;
  programDialog.value = false;
};
const addGrid = () => {
  if (grid.value) {
    addWidgetContent.value += 1; // 增加计数
    grid.value.addWidget({
      w: 1,
      h: 6,
      content: `部件${addWidgetContent.value}`
    });
  }
};
const params = {
  Status: "1",
  AllDates: new Date(Date.now()),
  QueryCriteria: "",
  Rows: "10",
  Page: 1,
  Sidx: "1",
  Sord: "1"
};
const chooseMaterial = async () => {
  const tb = await getMaterial(params);
  materialList.value = tb.data.list;
  console.log(tb);
  programDialog.value = true;
};

// 声明一个对象保存当前布局数据
const currentLayout = ref([]);
const saveLayout = () => {
  if (grid.value) {
    currentLayout.value = grid.value.save();
    console.log("当前布局:", currentLayout.value);
  } else { 
    currentLayout.value = [
      { x: 0, y:0 , w: 12, h: 6, content: "布局" }
    ]
  }
};

// // 监听弹窗打开事件
// watch(dialogVisible, async newVal => {
//   // 设置拖放事件
//   const container = document.querySelector(".right-box");
//   if (container) {
//     container.ondragover = e => {
//       e.preventDefault();
//     };
//     container.addEventListener("drop", addGrid);
//   }

//   const deleteGrid = document.querySelector(".delete-grid");
//   if (deleteGrid) {
//     deleteGrid.ondragover = e => e.preventDefault();
//     deleteGrid.addEventListener("drop", e => {
//       console.log(e);
//     });
//   }
// });

// 表单
const lanchFrom = ref({
  name: "",
  // lanchRespect: "",
  lanchtime: "All",
  // allDay: true,
  // beginTime: "",
  // endTime: "",
  putBegin: "",
  putEnd:""
});

const rules = reactive({
  name: [{ required: true, message: "请输入投放名称", trigger: "blur" }],
  lanchRespect: [
    {
      required: true,
      message: "请选择投放重复状态",
      trigger: "change"
    }
  ]
});

// 点击投放按钮后
const launch = () => {
  launchDialog.value = true;
};
// 设备选择弹窗
const equipmentDialog = ref(false);
const equipmentList = ref([]);
const lanchEquipment = async () => {
  const tb = await getEquipment(params);
  equipmentList.value = tb.data.list;
  console.log(tb);
  equipmentDialog.value = true;
};
// 选择的设备列表数据
const selectEquiment = ref([]);
const handleSelectEquipment = item => {
  if (item.selected) {
    selectEquiment.value.push(item);
  } else {
    selectEquiment.value = selectEquiment.value.filter(i => i.DeviceID !== item.DeviceID);
  }
};
const sureSelectEquipment = () => {
  console.log(selectEquiment.value);
  equipmentDialog.value = false;
}

// 确定添加投放
const addPutMark = async () => {
  // 调用保存模版方法获取模版坐标数据
  saveLayout();
  console.log(currentLayout.value)
  // const resquestBody = {
	// "Model": {
	// 	"CampaignID": 0,
	// 	"CampaignName": lanchFrom.value.name,
	// 	"PlaylistID": 0,
	// 	"DeviceID": 0,
	// 	"MMDeviceList": selectEquiment.value,
	// 	"MMLayoutTemplateEntity": {
	// 		"LayoutID": 0,
	// 		"LayoutName": "二分屏",
	// 		"LayoutDescription": "将屏从中分开展示节目效果",
	// 		"LayoutRows": 500,
	// 		"LayoutCols": 1000,
	// 		"TemplateGridCount": 2,
	// 		"RegionList": [
	// 			{
	// 				"RegionID": 0,
	// 				"LayoutID": 0,
	// 				"RegionName": "",
	// 				"StartX": 0,
	// 				"StartY": 0,
	// 				"RegionWidth": 6,
	// 				"RegionHeight": 6,
	// 				"CreatedBy": "",
	// 				"HtmlTemplate": "",
	// 				"PlaylistDetails": {
	// 					"DetailID": 0,
	// 					"PlaylistID": 0,
	// 					"ProgramName": "",
	// 					"RegionID": 0,
	// 					"PlaylistDetailXqList": [
	// 						{
	// 							"DetailXqID": 0,
	// 							"DetailID": 0,
	// 							"FileID": 0,
	// 							"Sequence": 1,
	// 							"AdjustedDuration": 10,
	// 							"MMFile": {
	// 								"FileID": list.value[0].FileID,
	// 								"FileName": list.value[0].FileName,
	// 								"FilePath": list.value[0].FilePath,
	// 								"FormatType": list.value[0].FormatType,
	// 								"FileSize": list.value[0].VideoDuration,
	// 								"VideoDuration": list.value[0].VideoDuration
	// 							}
	// 						},
	// 						{
	// 							"DetailXqID": 0,
	// 							"DetailID": 0,
	// 							"FileID": 0,
	// 							"Sequence": 2,
	// 							"AdjustedDuration": 10,
	// 							"MMFile": {
	// 							  "FileID": list.value[0].FileID,
	// 								"FileName": list.value[1].FileName,
	// 								"FilePath": list.value[1].FilePath,
	// 								"FormatType": list.value[1].FormatType,
	// 								"FileSize": list.value[1].VideoDuration,
	// 								"VideoDuration": list.value[1].VideoDuration
	// 							}
	// 						}
	// 					]
	// 				}
	// 			},
	// 			{
	// 				"RegionID": 0,
	// 				"LayoutID": 0,
	// 				"RegionName": "",
	// 				"StartX": 6,
	// 				"StartY": 0,
	// 				"RegionWidth": 6,
	// 				"RegionHeight": 6,
	// 				"CreatedBy": "",
	// 				"HtmlTemplate": "",
	// 				"PlaylistDetails": {
	// 					"DetailID": 0,
	// 					"PlaylistID": 0,
	// 					"ProgramName": "",
	// 					"RegionID": 0,
	// 					"PlaylistDetailXqList": [
	// 						{
	// 							"DetailXqID": 0,
	// 							"DetailID": 0,
	// 							"FileID": 0,
	// 							"Sequence": 1,
	// 							"AdjustedDuration": 10,
	// 							"MMFile": {
	// 								"FileID": 3,
	// 								"FileName": "3ea8934a-b383-4781-912e-775ee218b2c1.mp4",
	// 								"FilePath": "",
	// 								"FormatType": "",
	// 								"FileSize": 0,
	// 								"VideoDuration": 0
	// 							}
	// 						}
	// 					]
	// 				}
	// 			}
	// 		]
	// 	},
	// 	"LayoutID": 0,
	// 	"StartTime": "2025-05-08T12:16:09.183Z",
	// 	"EndTime": "2025-05-08T12:16:09.183Z"
	// }
  // }
  // const res = await addPut(resquestBody);
  // console.log(res);
  
}
</script>
<style lang="scss" scoped >
body {
  padding: 0;
  margin: 0;
}

#home {
  display: flex;
  width: 100%;
  height: calc(100vh - 34px - 68px);
  margin: 0;
  padding: 0;
  background-color: #ffffff;
}

.grid-stack {
  /* 根据页面结构调整 */
  height: 78vh;
}

.left-box {
  padding-top: 10px;
  display: inline;
  display: flex;
  flex-direction: column;
  width: 15%;
  border-right: 2px solid #ebebeb;
  background-color: rgb(49, 49, 146);
}
.delete-grid {
  width: 100px;
  height: 100px;
  text-align: center;
  border: 2px solid #a7a7a7;
  margin: 10px auto;
}

.add-grid {
  width: 100px;
  height: 100px;
  border: 2px solid #808080;
  margin: 10px auto;
  text-align: center;
}

.right-box {
  width: 85%;
  background-color: #ebebeb;
}
.aspect-ratio-16-9 {
  width: 40%;
  aspect-ratio: 16/9;
  border: 1px solid gray;
  margin: 0 auto;
}
.aspect-ratio-9-16 {
  width: 13%;
  aspect-ratio: 9/16;
  border: 1px solid gray;
  margin: 0 auto;
}
.p-0 {
  padding: 0 !important; /* 去除内边距 */
}
.m-0 {
  margin: 0 !important; /* 去除外边距 */
}
.grid-stack-item-conten:active {
      background-color: rgb(67, 150, 202);
      color: white;
    }
    .grid-stack-item-conten:focus {
      background-color: rgb(67, 150, 202);
      color: white;
    }
</style>
<style lang="scss">
.grid-stack-item-content {
  background-color: #ffffff !important;
  text-align: center;
  border: 1px solid gray !important;
}

.grid-stack-item-content {
  padding: 0 !important;
  /* 去除内边距 */
  overflow: hidden !important;
  /* 隐藏溢出部分 */
}
/* 弹性容器：控制卡片横向排列 */
.material-container {
  display: flex; /* 启用弹性布局 */
  flex-wrap: wrap; /* 允许卡片换行 */
  gap: 16px; /* 卡片间距（可调整） */
  padding: 16px; /* 容器内边距，避免卡片贴边 */
}

/* 卡片样式：控制单个卡片尺寸和弹性比例 */
.material-card {
  flex: 0 0 calc(25% - 16px); /* 一行4个：25%宽度 - 间距 */
  max-width: calc(25% - 16px); /* 适配响应式布局 */
  min-width: 200px; /* 卡片最小宽度，防止过小 */
}

/* 响应式调整：屏幕较小时减少每行卡片数量 */
@media (max-width: 992px) {
  /* 可根据弹窗宽度调整断点 */
  .material-card {
    flex: 0 0 calc(33.33% - 16px); /* 一行3个 */
  }
}

@media (max-width: 768px) {
  .material-card {
    flex: 0 0 calc(50% - 16px); /* 一行2个 */
  }
}
.equMsg {
  color: rgb(88, 88, 88);
}
</style>
